fileFormatVersion: 2
guid: 2bf2215389819b342a877f12b9473126
ModelImporter:
  serializedVersion: 22
  fileIDToRecycleName:
    100000: //RootNode
    100002: Cube
    100004: Cube.001
    100006: Cube.002
    100008: Cube.003
    100010: Cube.004
    100012: Cylinder
    100014: Root
    400000: //RootNode
    400002: Cube
    400004: Cube.001
    400006: Cube.002
    400008: Cube.003
    400010: Cube.004
    400012: Cylinder
    400014: Root
    2100000: No Name
    2300000: Cube
    2300002: Cube.001
    2300004: Cube.002
    2300006: Cube.003
    2300008: Cube.004
    2300010: Cylinder
    2300012: Root
    3300000: Cube
    3300002: Cube.001
    3300004: Cube.002
    3300006: Cube.003
    3300008: Cube.004
    3300010: Cylinder
    3300012: Root
    4300000: Cube.004
    4300002: Cylinder
    4300004: Root
    4300006: Cube.003
    4300008: Cube.002
    4300010: Cube.001
    4300012: Cube
    6400000: Cube
    6400002: Cube.001
    6400004: Cube.002
    6400006: Cube.003
    6400008: Cube.004
    6400010: Cylinder
    6400012: Root
    2186277476908879412: ImportLogs
  externalObjects: {}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 2
    meshCompression: 0
    addColliders: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
