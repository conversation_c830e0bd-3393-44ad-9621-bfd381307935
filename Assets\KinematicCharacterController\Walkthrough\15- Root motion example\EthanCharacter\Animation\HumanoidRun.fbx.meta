fileFormatVersion: 2
guid: 1cb8ed3cbba15f0479fbae54e0a963df
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Character_Ctrl:Reference
    100002: Chest
    100004: ChestEndEffector
    100006: ChestOriginEffector
    100008: chestProxy_geo
    100010: //RootNode
    100012: Head
    100014: Head 1
    100016: HeadEffector
    100018: headProxy_geo
    100020: HeadTop_End
    100022: Hips
    100024: Hips 1
    100026: HipsEffector
    100028: Jaw
    100030: JawEND
    100032: jawProxy_geo
    100034: l_ankleProxy_geo
    100036: l_ballProxy_geo
    100038: l_clavicleProxy_geo
    100040: l_erbowProxy_geo
    100042: l_hipProxy_geo
    100044: l_indexProxy_01_geo
    100046: l_indexProxy_02_geo
    100048: l_indexProxy_03_geo
    100050: l_kneeProxy_geo
    100052: l_middleProxy_01_geo
    100054: l_middleProxy_02_geo
    100056: l_middleProxy_03_geo
    100058: l_pinkyProxy_01_geo
    100060: l_pinkyProxy_02_geo
    100062: l_pinkyProxy_03_geo
    100064: l_ringProxy_01_geo
    100066: l_ringProxy_02_geo
    100068: l_ringProxy_03_geo
    100070: l_shourderProxy_geo
    100072: l_thumbProxy_01_geo
    100074: l_thumbProxy_02_geo
    100076: l_thumbProxy_03_geo
    100078: l_UNI_eye
    100080: l_wristProxy_geo
    100082: LeftAnkleEffector
    100084: LeftArm
    100086: LeftArm 1
    100088: LeftCheek
    100090: LeftElbowEffector
    100092: LeftEye
    100094: LeftEyelidLower
    100096: LeftEyelidUpper
    100098: LeftFoot
    100100: LeftFoot 1
    100102: LeftForeArm
    100104: LeftForeArm 1
    100106: LeftHand
    100108: LeftHand 1
    100110: LeftHandIndex1
    100112: LeftHandIndex13
    100114: LeftHandIndex17
    100116: LeftHandIndex2
    100118: LeftHandIndex3
    100120: LeftHandIndex4
    100122: LeftHandIndex5
    100124: LeftHandIndex6
    100126: LeftHandIndexEffector
    100128: LeftHandMiddle1
    100130: LeftHandMiddle13
    100132: LeftHandMiddle17
    100134: LeftHandMiddle2
    100136: LeftHandMiddle3
    100138: LeftHandMiddle4
    100140: LeftHandMiddle5
    100142: LeftHandMiddle6
    100144: LeftHandMiddleEffector
    100146: LeftHandPinky1
    100148: LeftHandPinky13
    100150: LeftHandPinky17
    100152: LeftHandPinky2
    100154: LeftHandPinky3
    100156: LeftHandPinky4
    100158: LeftHandPinky5
    100160: LeftHandPinky6
    100162: LeftHandPinkyEffector
    100164: LeftHandRing1
    100166: LeftHandRing13
    100168: LeftHandRing17
    100170: LeftHandRing2
    100172: LeftHandRing3
    100174: LeftHandRing4
    100176: LeftHandRing5
    100178: LeftHandRing6
    100180: LeftHandRingEffector
    100182: LeftHandThumb1
    100184: LeftHandThumb13
    100186: LeftHandThumb17
    100188: LeftHandThumb2
    100190: LeftHandThumb3
    100192: LeftHandThumb4
    100194: LeftHandThumb5
    100196: LeftHandThumb6
    100198: LeftHandThumbEffector
    100200: LeftHipEffector
    100202: LeftInnerBrow
    100204: LeftIOuterBrow
    100206: LeftKneeEffector
    100208: LeftLeg
    100210: LeftLeg 1
    100212: LeftLipCorner
    100214: LeftLipLower
    100216: LeftLipUpper
    100218: LeftNostril
    100220: LeftShoulder
    100222: LeftShoulder 1
    100224: LeftShoulderEffector
    100226: LeftToes
    100228: LeftUpLeg
    100230: LeftUpLeg 1
    100232: LeftWristEffector
    100234: LToeBase_End2
    100236: LToeBase_End3
    100238: Neck
    100240: Neck 1
    100242: neckProxy_geo
    100244: pelvisProxy_geo
    100246: r_ankleProxy_geo
    100248: r_ballProxy_geo
    100250: r_clavicleProxy_geo
    100252: r_erbowProxy_geo
    100254: r_hipProxy_geo
    100256: r_indexProxy_01_geo
    100258: r_indexProxy_02_geo
    100260: r_indexProxy_03_geo
    100262: r_kneeProxy_geo
    100264: r_middleProxy_01_geo
    100266: r_middleProxy_02_geo
    100268: r_middleProxy_03_geo
    100270: r_pinkyProxy_01_geo
    100272: r_pinkyProxy_02_geo
    100274: r_pinkyProxy_03_geo
    100276: r_ringProxy_01_geo
    100278: r_ringProxy_02_geo
    100280: r_ringProxy_03_geo
    100282: r_shourderProxy_geo
    100284: r_thumbProxy_01_geo
    100286: r_thumbProxy_02_geo
    100288: r_thumbProxy_03_geo
    100290: r_UNI_eye
    100292: r_wristProxy_geo
    100294: Reference
    100296: RightAnkleEffector
    100298: RightArm
    100300: RightArm 1
    100302: RightCheek
    100304: RightElbowEffector
    100306: RightEye
    100308: RightEyelidLower
    100310: RightEyelidUpper
    100312: RightFoot
    100314: RightFoot 1
    100316: RightForeArm
    100318: RightForeArm 1
    100320: RightHand
    100322: RightHand 1
    100324: RightHandIndex1
    100326: RightHandIndex2
    100328: RightHandIndex3
    100330: RightHandIndex4
    100332: RightHandIndex5
    100334: RightHandIndex6
    100336: RightHandIndexEffector
    100338: RightHandMiddle1
    100340: RightHandMiddle2
    100342: RightHandMiddle3
    100344: RightHandMiddle4
    100346: RightHandMiddle5
    100348: RightHandMiddle6
    100350: RightHandMiddleEffector
    100352: RightHandPinky1
    100354: RightHandPinky2
    100356: RightHandPinky3
    100358: RightHandPinky4
    100360: RightHandPinky5
    100362: RightHandPinky6
    100364: RightHandPinkyEffector
    100366: RightHandRing1
    100368: RightHandRing2
    100370: RightHandRing3
    100372: RightHandRing4
    100374: RightHandRing5
    100376: RightHandRing6
    100378: RightHandRingEffector
    100380: RightHandThumb1
    100382: RightHandThumb2
    100384: RightHandThumb3
    100386: RightHandThumb4
    100388: RightHandThumb5
    100390: RightHandThumb6
    100392: RightHandThumbEffector
    100394: RightHipEffector
    100396: RightInnerBrow
    100398: RightIOuterBrow
    100400: RightKneeEffector
    100402: RightLeg
    100404: RightLeg 1
    100406: RightLipCorner
    100408: RightLipLower
    100410: RightLipUpper
    100412: RightNostril
    100414: RightShoulder
    100416: RightShoulder 1
    100418: RightShoulderEffector
    100420: RightToes
    100422: RightUpLeg
    100424: RightUpLeg 1
    100426: RightWristEffector
    100428: Spine
    100430: Spine 1
    100432: Spine1
    100434: spineProxy_geo
    100436: TongueBack
    100438: TongueTip
    100440: UNI_01_Lower_teethProxy
    100442: UNI_01_TongueBaseProxy
    100444: UNI_01_TongueTipProxy
    100446: UNI_01_Upper_teethProxy
    400000: Character_Ctrl:Reference
    400002: Chest
    400004: ChestEndEffector
    400006: ChestOriginEffector
    400008: chestProxy_geo
    400010: //RootNode
    400012: Head
    400014: Head 1
    400016: HeadEffector
    400018: headProxy_geo
    400020: HeadTop_End
    400022: Hips
    400024: Hips 1
    400026: HipsEffector
    400028: Jaw
    400030: JawEND
    400032: jawProxy_geo
    400034: l_ankleProxy_geo
    400036: l_ballProxy_geo
    400038: l_clavicleProxy_geo
    400040: l_erbowProxy_geo
    400042: l_hipProxy_geo
    400044: l_indexProxy_01_geo
    400046: l_indexProxy_02_geo
    400048: l_indexProxy_03_geo
    400050: l_kneeProxy_geo
    400052: l_middleProxy_01_geo
    400054: l_middleProxy_02_geo
    400056: l_middleProxy_03_geo
    400058: l_pinkyProxy_01_geo
    400060: l_pinkyProxy_02_geo
    400062: l_pinkyProxy_03_geo
    400064: l_ringProxy_01_geo
    400066: l_ringProxy_02_geo
    400068: l_ringProxy_03_geo
    400070: l_shourderProxy_geo
    400072: l_thumbProxy_01_geo
    400074: l_thumbProxy_02_geo
    400076: l_thumbProxy_03_geo
    400078: l_UNI_eye
    400080: l_wristProxy_geo
    400082: LeftAnkleEffector
    400084: LeftArm
    400086: LeftArm 1
    400088: LeftCheek
    400090: LeftElbowEffector
    400092: LeftEye
    400094: LeftEyelidLower
    400096: LeftEyelidUpper
    400098: LeftFoot
    400100: LeftFoot 1
    400102: LeftForeArm
    400104: LeftForeArm 1
    400106: LeftHand
    400108: LeftHand 1
    400110: LeftHandIndex1
    400112: LeftHandIndex13
    400114: LeftHandIndex17
    400116: LeftHandIndex2
    400118: LeftHandIndex3
    400120: LeftHandIndex4
    400122: LeftHandIndex5
    400124: LeftHandIndex6
    400126: LeftHandIndexEffector
    400128: LeftHandMiddle1
    400130: LeftHandMiddle13
    400132: LeftHandMiddle17
    400134: LeftHandMiddle2
    400136: LeftHandMiddle3
    400138: LeftHandMiddle4
    400140: LeftHandMiddle5
    400142: LeftHandMiddle6
    400144: LeftHandMiddleEffector
    400146: LeftHandPinky1
    400148: LeftHandPinky13
    400150: LeftHandPinky17
    400152: LeftHandPinky2
    400154: LeftHandPinky3
    400156: LeftHandPinky4
    400158: LeftHandPinky5
    400160: LeftHandPinky6
    400162: LeftHandPinkyEffector
    400164: LeftHandRing1
    400166: LeftHandRing13
    400168: LeftHandRing17
    400170: LeftHandRing2
    400172: LeftHandRing3
    400174: LeftHandRing4
    400176: LeftHandRing5
    400178: LeftHandRing6
    400180: LeftHandRingEffector
    400182: LeftHandThumb1
    400184: LeftHandThumb13
    400186: LeftHandThumb17
    400188: LeftHandThumb2
    400190: LeftHandThumb3
    400192: LeftHandThumb4
    400194: LeftHandThumb5
    400196: LeftHandThumb6
    400198: LeftHandThumbEffector
    400200: LeftHipEffector
    400202: LeftInnerBrow
    400204: LeftIOuterBrow
    400206: LeftKneeEffector
    400208: LeftLeg
    400210: LeftLeg 1
    400212: LeftLipCorner
    400214: LeftLipLower
    400216: LeftLipUpper
    400218: LeftNostril
    400220: LeftShoulder
    400222: LeftShoulder 1
    400224: LeftShoulderEffector
    400226: LeftToes
    400228: LeftUpLeg
    400230: LeftUpLeg 1
    400232: LeftWristEffector
    400234: LToeBase_End2
    400236: LToeBase_End3
    400238: Neck
    400240: Neck 1
    400242: neckProxy_geo
    400244: pelvisProxy_geo
    400246: r_ankleProxy_geo
    400248: r_ballProxy_geo
    400250: r_clavicleProxy_geo
    400252: r_erbowProxy_geo
    400254: r_hipProxy_geo
    400256: r_indexProxy_01_geo
    400258: r_indexProxy_02_geo
    400260: r_indexProxy_03_geo
    400262: r_kneeProxy_geo
    400264: r_middleProxy_01_geo
    400266: r_middleProxy_02_geo
    400268: r_middleProxy_03_geo
    400270: r_pinkyProxy_01_geo
    400272: r_pinkyProxy_02_geo
    400274: r_pinkyProxy_03_geo
    400276: r_ringProxy_01_geo
    400278: r_ringProxy_02_geo
    400280: r_ringProxy_03_geo
    400282: r_shourderProxy_geo
    400284: r_thumbProxy_01_geo
    400286: r_thumbProxy_02_geo
    400288: r_thumbProxy_03_geo
    400290: r_UNI_eye
    400292: r_wristProxy_geo
    400294: Reference
    400296: RightAnkleEffector
    400298: RightArm
    400300: RightArm 1
    400302: RightCheek
    400304: RightElbowEffector
    400306: RightEye
    400308: RightEyelidLower
    400310: RightEyelidUpper
    400312: RightFoot
    400314: RightFoot 1
    400316: RightForeArm
    400318: RightForeArm 1
    400320: RightHand
    400322: RightHand 1
    400324: RightHandIndex1
    400326: RightHandIndex2
    400328: RightHandIndex3
    400330: RightHandIndex4
    400332: RightHandIndex5
    400334: RightHandIndex6
    400336: RightHandIndexEffector
    400338: RightHandMiddle1
    400340: RightHandMiddle2
    400342: RightHandMiddle3
    400344: RightHandMiddle4
    400346: RightHandMiddle5
    400348: RightHandMiddle6
    400350: RightHandMiddleEffector
    400352: RightHandPinky1
    400354: RightHandPinky2
    400356: RightHandPinky3
    400358: RightHandPinky4
    400360: RightHandPinky5
    400362: RightHandPinky6
    400364: RightHandPinkyEffector
    400366: RightHandRing1
    400368: RightHandRing2
    400370: RightHandRing3
    400372: RightHandRing4
    400374: RightHandRing5
    400376: RightHandRing6
    400378: RightHandRingEffector
    400380: RightHandThumb1
    400382: RightHandThumb2
    400384: RightHandThumb3
    400386: RightHandThumb4
    400388: RightHandThumb5
    400390: RightHandThumb6
    400392: RightHandThumbEffector
    400394: RightHipEffector
    400396: RightInnerBrow
    400398: RightIOuterBrow
    400400: RightKneeEffector
    400402: RightLeg
    400404: RightLeg 1
    400406: RightLipCorner
    400408: RightLipLower
    400410: RightLipUpper
    400412: RightNostril
    400414: RightShoulder
    400416: RightShoulder 1
    400418: RightShoulderEffector
    400420: RightToes
    400422: RightUpLeg
    400424: RightUpLeg 1
    400426: RightWristEffector
    400428: Spine
    400430: Spine 1
    400432: Spine1
    400434: spineProxy_geo
    400436: TongueBack
    400438: TongueTip
    400440: UNI_01_Lower_teethProxy
    400442: UNI_01_TongueBaseProxy
    400444: UNI_01_TongueTipProxy
    400446: UNI_01_Upper_teethProxy
    2300000: chestProxy_geo
    2300002: headProxy_geo
    2300004: jawProxy_geo
    2300006: l_ankleProxy_geo
    2300008: l_ballProxy_geo
    2300010: l_clavicleProxy_geo
    2300012: l_erbowProxy_geo
    2300014: l_hipProxy_geo
    2300016: l_indexProxy_01_geo
    2300018: l_indexProxy_02_geo
    2300020: l_indexProxy_03_geo
    2300022: l_kneeProxy_geo
    2300024: l_middleProxy_01_geo
    2300026: l_middleProxy_02_geo
    2300028: l_middleProxy_03_geo
    2300030: l_pinkyProxy_01_geo
    2300032: l_pinkyProxy_02_geo
    2300034: l_pinkyProxy_03_geo
    2300036: l_ringProxy_01_geo
    2300038: l_ringProxy_02_geo
    2300040: l_ringProxy_03_geo
    2300042: l_shourderProxy_geo
    2300044: l_thumbProxy_01_geo
    2300046: l_thumbProxy_02_geo
    2300048: l_thumbProxy_03_geo
    2300050: l_UNI_eye
    2300052: l_wristProxy_geo
    2300054: neckProxy_geo
    2300056: pelvisProxy_geo
    2300058: r_ankleProxy_geo
    2300060: r_ballProxy_geo
    2300062: r_clavicleProxy_geo
    2300064: r_erbowProxy_geo
    2300066: r_hipProxy_geo
    2300068: r_indexProxy_01_geo
    2300070: r_indexProxy_02_geo
    2300072: r_indexProxy_03_geo
    2300074: r_kneeProxy_geo
    2300076: r_middleProxy_01_geo
    2300078: r_middleProxy_02_geo
    2300080: r_middleProxy_03_geo
    2300082: r_pinkyProxy_01_geo
    2300084: r_pinkyProxy_02_geo
    2300086: r_pinkyProxy_03_geo
    2300088: r_ringProxy_01_geo
    2300090: r_ringProxy_02_geo
    2300092: r_ringProxy_03_geo
    2300094: r_shourderProxy_geo
    2300096: r_thumbProxy_01_geo
    2300098: r_thumbProxy_02_geo
    2300100: r_thumbProxy_03_geo
    2300102: r_UNI_eye
    2300104: r_wristProxy_geo
    2300106: spineProxy_geo
    2300108: UNI_01_Lower_teethProxy
    2300110: UNI_01_TongueBaseProxy
    2300112: UNI_01_TongueTipProxy
    2300114: UNI_01_Upper_teethProxy
    3300000: chestProxy_geo
    3300002: headProxy_geo
    3300004: jawProxy_geo
    3300006: l_ankleProxy_geo
    3300008: l_ballProxy_geo
    3300010: l_clavicleProxy_geo
    3300012: l_erbowProxy_geo
    3300014: l_hipProxy_geo
    3300016: l_indexProxy_01_geo
    3300018: l_indexProxy_02_geo
    3300020: l_indexProxy_03_geo
    3300022: l_kneeProxy_geo
    3300024: l_middleProxy_01_geo
    3300026: l_middleProxy_02_geo
    3300028: l_middleProxy_03_geo
    3300030: l_pinkyProxy_01_geo
    3300032: l_pinkyProxy_02_geo
    3300034: l_pinkyProxy_03_geo
    3300036: l_ringProxy_01_geo
    3300038: l_ringProxy_02_geo
    3300040: l_ringProxy_03_geo
    3300042: l_shourderProxy_geo
    3300044: l_thumbProxy_01_geo
    3300046: l_thumbProxy_02_geo
    3300048: l_thumbProxy_03_geo
    3300050: l_UNI_eye
    3300052: l_wristProxy_geo
    3300054: neckProxy_geo
    3300056: pelvisProxy_geo
    3300058: r_ankleProxy_geo
    3300060: r_ballProxy_geo
    3300062: r_clavicleProxy_geo
    3300064: r_erbowProxy_geo
    3300066: r_hipProxy_geo
    3300068: r_indexProxy_01_geo
    3300070: r_indexProxy_02_geo
    3300072: r_indexProxy_03_geo
    3300074: r_kneeProxy_geo
    3300076: r_middleProxy_01_geo
    3300078: r_middleProxy_02_geo
    3300080: r_middleProxy_03_geo
    3300082: r_pinkyProxy_01_geo
    3300084: r_pinkyProxy_02_geo
    3300086: r_pinkyProxy_03_geo
    3300088: r_ringProxy_01_geo
    3300090: r_ringProxy_02_geo
    3300092: r_ringProxy_03_geo
    3300094: r_shourderProxy_geo
    3300096: r_thumbProxy_01_geo
    3300098: r_thumbProxy_02_geo
    3300100: r_thumbProxy_03_geo
    3300102: r_UNI_eye
    3300104: r_wristProxy_geo
    3300106: spineProxy_geo
    3300108: UNI_01_Lower_teethProxy
    3300110: UNI_01_TongueBaseProxy
    3300112: UNI_01_TongueTipProxy
    3300114: UNI_01_Upper_teethProxy
    4300000: l_UNI_eye
    4300002: r_UNI_eye
    4300004: UNI_01_TongueBaseProxy
    4300006: UNI_01_TongueTipProxy
    4300008: UNI_01_Lower_teethProxy
    4300010: jawProxy_geo
    4300012: headProxy_geo
    4300014: UNI_01_Upper_teethProxy
    4300016: neckProxy_geo
    4300018: r_pinkyProxy_03_geo
    4300020: r_pinkyProxy_02_geo
    4300022: r_pinkyProxy_01_geo
    4300024: r_ringProxy_03_geo
    4300026: r_ringProxy_02_geo
    4300028: r_ringProxy_01_geo
    4300030: r_middleProxy_03_geo
    4300032: r_middleProxy_02_geo
    4300034: r_middleProxy_01_geo
    4300036: r_indexProxy_03_geo
    4300038: r_indexProxy_02_geo
    4300040: r_indexProxy_01_geo
    4300042: r_thumbProxy_03_geo
    4300044: r_thumbProxy_02_geo
    4300046: r_thumbProxy_01_geo
    4300048: r_wristProxy_geo
    4300050: r_erbowProxy_geo
    4300052: r_shourderProxy_geo
    4300054: r_clavicleProxy_geo
    4300056: chestProxy_geo
    4300058: l_pinkyProxy_03_geo
    4300060: l_pinkyProxy_02_geo
    4300062: l_pinkyProxy_01_geo
    4300064: l_ringProxy_03_geo
    4300066: l_ringProxy_02_geo
    4300068: l_ringProxy_01_geo
    4300070: l_middleProxy_03_geo
    4300072: l_middleProxy_02_geo
    4300074: l_middleProxy_01_geo
    4300076: l_indexProxy_03_geo
    4300078: l_indexProxy_02_geo
    4300080: l_indexProxy_01_geo
    4300082: l_thumbProxy_03_geo
    4300084: l_thumbProxy_02_geo
    4300086: l_thumbProxy_01_geo
    4300088: l_wristProxy_geo
    4300090: l_erbowProxy_geo
    4300092: l_shourderProxy_geo
    4300094: l_clavicleProxy_geo
    4300096: spineProxy_geo
    4300098: r_ballProxy_geo
    4300100: r_ankleProxy_geo
    4300102: r_kneeProxy_geo
    4300104: r_hipProxy_geo
    4300106: pelvisProxy_geo
    4300108: l_ballProxy_geo
    4300110: l_ankleProxy_geo
    4300112: l_kneeProxy_geo
    4300114: l_hipProxy_geo
    7400000: HumanoidRun
    9500000: //RootNode
  materials:
    importMaterials: 0
    materialName: 1
    materialSearch: 2
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleRotations: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 0
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: HumanoidRun
      takeName: _24_a_U1_M_P_RunForward_NtrlFaceFwd__Fb_p0_No_0_PJ_10
      firstFrame: 335.9
      lastFrame: 353.7
      wrapMode: 0
      orientationOffsetY: -1.2
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 88
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.01
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToes
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToes
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftEye
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightEye
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Jaw
      humanName: Jaw
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Run(Clone)
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Hips
      position: {x: -0.000000020489097, y: 0.958501, z: 0.059998296}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftUpLeg
      position: {x: -0.0754495, y: -0.04566402, z: -6.217249e-17}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLeg
      position: {x: -0.020550499, y: -0.40912998, z: -0.00071864796}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftFoot
      position: {x: -0.0051529994, y: -0.4231559, z: -0.027648851}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftToes
      position: {x: -0.007487, y: -0.0731673, z: 0.14542712}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightUpLeg
      position: {x: 0.075449534, y: -0.04566399, z: -6.217249e-17}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLeg
      position: {x: 0.020550467, y: -0.40913, z: -0.00071864796}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightFoot
      position: {x: 0.0051529994, y: -0.4231559, z: -0.027648851}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightToes
      position: {x: 0.007487, y: -0.0731673, z: 0.1454275}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Spine
      position: {x: -4.7739588e-17, y: 0.092263184, z: 0.015771331}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Chest
      position: {x: 3.5527136e-17, y: 0.16254029, z: -0.0016560555}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftShoulder
      position: {x: -0.038285997, y: 0.2216225, z: -0.017063085}
      rotation: {x: -0.02901484, y: -0.07803791, z: 0.1478155, w: 0.9855044}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftArm
      position: {x: -0.10050205, y: 0.000000001125083, z: -1.9039074e-10}
      rotation: {x: 0.0065737762, y: 0.07236942, z: -0.1361931, w: 0.9880137}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftForeArm
      position: {x: -0.2540493, y: -1.0551325e-10, z: 1.09112684e-10}
      rotation: {x: 0.37014812, y: 0.03247756, z: -0.006699631, w: 0.9283807}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHand
      position: {x: -0.24638927, y: -1.1574698e-10, z: 1.1358061e-11}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex1
      position: {x: -0.0751258, y: -0.0078414045, z: 0.032652643}
      rotation: {x: 0.042805295, y: 0.05632816, z: 0.06901114, w: 0.99510425}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex2
      position: {x: -0.03979728, y: 0.000049808412, z: 0.0011857506}
      rotation: {x: -0.11841995, y: 0.01500381, z: 0.016670156, w: 0.99271035}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex3
      position: {x: -0.027968477, y: -0.000000006416276, z: -0.000000051434675}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle1
      position: {x: -0.076023825, y: -0.0018851344, z: 0.010141229}
      rotation: {x: -0.033341967, y: 0.07042229, z: 0.07230802, w: 0.9943342}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle2
      position: {x: -0.044280436, y: 0.00000479887, z: -0.00042540036}
      rotation: {x: -0.033172157, y: -0.0051259603, z: 0.011490114, w: 0.9993705}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle3
      position: {x: -0.033964828, y: -0.000000012184439, z: 0.000000003753109}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky1
      position: {x: -0.06565995, y: -0.007825106, z: -0.032251246}
      rotation: {x: -0.110300295, y: 0.079448596, z: 0.0742732, w: 0.9879298}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky2
      position: {x: -0.030805448, y: -0.000030874577, z: -0.0014480774}
      rotation: {x: -0.072170265, y: -0.026308026, z: 0.013470372, w: 0.9969544}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky3
      position: {x: -0.023064027, y: -0.0000064025903, z: 0.000000018201217}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing1
      position: {x: -0.07030211, y: -0.0037453093, z: -0.011411792}
      rotation: {x: 0.015795682, y: 0.09177202, z: 0.06791128, w: 0.9933361}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing2
      position: {x: -0.043135457, y: -0.000020882291, z: -0.0022351781}
      rotation: {x: -0.13446514, y: -0.026096364, z: 0.008734329, w: 0.9905361}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing3
      position: {x: -0.030835565, y: 1.5784053e-10, z: -0.000000016455102}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb1
      position: {x: -0.014231241, y: -0.012377825, z: 0.025531668}
      rotation: {x: -0.2238929, y: -0.0758366, z: -0.1291156, w: 0.9630421}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb2
      position: {x: -0.016374, y: -0.00529, z: 0.023491409}
      rotation: {x: -0.0260623, y: 0.09668697, z: 0.0036068659, w: 0.9949671}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb3
      position: {x: -0.02546, y: -0.00764, z: 0.020833}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Neck
      position: {x: -0, y: 0.2590093, z: -0.032413255}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Head
      position: {x: 5.7731595e-17, y: 0.08307038, z: 0.0113267815}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Jaw
      position: {x: 1.7347234e-20, y: 0.0111267585, z: 0.010327543}
      rotation: {x: 0.21924005, y: -0, z: -0, w: 0.975671}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: JawEND
      position: {x: -1.7347234e-20, y: -0.04828876, z: 0.07185171}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipCorner
      position: {x: -0.032843262, y: -0.01657876, z: 0.066121764}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipLower
      position: {x: -0.014250817, y: -0.02168876, z: 0.08224063}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipCorner
      position: {x: 0.03284, y: -0.01657876, z: 0.066118784}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipLower
      position: {x: 0.014250817, y: -0.02168876, z: 0.082238786}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: TongueBack
      position: {x: -1.7347234e-20, y: -0.022869369, z: 0.010095409}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: TongueTip
      position: {x: -1.7347234e-20, y: -0.023278812, z: 0.03832271}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftCheek
      position: {x: -0.054244027, y: 0.03370195, z: 0.0594304}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEye
      position: {x: -0.020848233, y: 0.0825027, z: 0.055427432}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEyelidLower
      position: {x: -0.035618957, y: 0.06507366, z: 0.07623474}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEyelidUpper
      position: {x: -0.034406897, y: 0.10060814, z: 0.08020531}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftInnerBrow
      position: {x: -0.012062691, y: 0.118765265, z: 0.093466826}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftIOuterBrow
      position: {x: -0.05503987, y: 0.11482529, z: 0.061777398}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipUpper
      position: {x: -0.014501322, y: -0.005111811, z: 0.09461884}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftNostril
      position: {x: -0.0179, y: 0.026312828, z: 0.0908674}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightCheek
      position: {x: 0.054239996, y: 0.033702828, z: 0.0594274}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEye
      position: {x: 0.020849999, y: 0.08250283, z: 0.0554274}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEyelidLower
      position: {x: 0.03562, y: 0.06507283, z: 0.0762374}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEyelidUpper
      position: {x: 0.03441, y: 0.10061283, z: 0.08020739}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightInnerBrow
      position: {x: 0.012062687, y: 0.118765265, z: 0.093466826}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightIOuterBrow
      position: {x: 0.055040002, y: 0.11482283, z: 0.061777398}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipUpper
      position: {x: 0.014501322, y: -0.0051071714, z: 0.094617404}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightNostril
      position: {x: 0.0179, y: 0.026308905, z: 0.09087062}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightShoulder
      position: {x: 0.038286015, y: 0.22162114, z: -0.017063085}
      rotation: {x: 0.1579978, y: 0.9867513, z: -0.013299583, w: -0.034375474}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightArm
      position: {x: -0.100501455, y: -0.0000024966455, z: -0.00000005228366}
      rotation: {x: 0.13541101, y: 0.98766977, z: -0.004818486, w: 0.07841701}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightForeArm
      position: {x: 0.25342825, y: 0.006011353, z: -0.016704524}
      rotation: {x: 0.25265744, y: 0.024327299, z: -0.026384478, w: 0.96689}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHand
      position: {x: 0.2453737, y: 0.021641772, z: 0.005550465}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex1
      position: {x: 0.0747695, y: -0.0012430536, z: 0.034344498}
      rotation: {x: 0.050132476, y: 0.10633009, z: -0.025096685, w: 0.99274915}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex2
      position: {x: 0.0370584, y: 0.00072612107, z: 0.014538894}
      rotation: {x: -0.12210108, y: 0.026184548, z: 0.03848509, w: 0.99142563}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex3
      position: {x: 0.025225038, y: -0.0049664653, z: 0.011012146}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle1
      position: {x: 0.075647645, y: 0.0047914027, z: 0.011853182}
      rotation: {x: -0.026879195, y: -0.0053047896, z: -0.033220712, w: 0.99907243}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle2
      position: {x: 0.043809064, y: 0.00019418815, z: 0.006454936}
      rotation: {x: -0.039818257, y: -0.04374049, z: 0.09885875, w: 0.993342}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle3
      position: {x: 0.03307247, y: -0.007547537, z: 0.0016898462}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky1
      position: {x: 0.06680334, y: -0.0019941085, z: -0.030756146}
      rotation: {x: -0.11187588, y: -0.25872952, z: 0.0088018915, w: 0.9594088}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky2
      position: {x: 0.028530842, y: -0.001397143, z: -0.011623796}
      rotation: {x: -0.07357618, y: 0.009609909, z: 0.0022198618, w: 0.9972409}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky3
      position: {x: 0.02142686, y: -0.00055350893, z: -0.008516608}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing1
      position: {x: 0.070598476, y: 0.0024570965, z: -0.009821458}
      rotation: {x: 0.01820465, y: -0.13375518, z: -0.008969554, w: 0.9908066}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing2
      position: {x: 0.042887185, y: -0.0013753821, z: -0.0049458584}
      rotation: {x: -0.12681748, y: 0.0007345817, z: 0.115031, w: 0.98523337}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing3
      position: {x: 0.029500604, y: -0.0076929354, z: -0.004622256}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb1
      position: {x: 0.014684916, y: -0.011104942, z: 0.025858095}
      rotation: {x: -0.16873905, y: 0.028051713, z: 0.11700559, w: 0.9782893}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb2
      position: {x: 0.016374, y: -0.00529, z: 0.02349136}
      rotation: {x: -0.026062516, y: -0.09668957, z: -0.003607418, w: 0.99496675}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb3
      position: {x: 0.02546, y: -0.00764, z: 0.020833}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: e8914d097ece7cc48a83d5fccd4098c0,
    type: 3}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
