fileFormatVersion: 2
guid: b235179bd2a63d1468dd430670338c55
timeCreated: **********
licenseType: Store
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: char_cyberKid_Badge
    100002: char_ethan_body
    100004: char_ethan_glasses
    100006: char_ethan_Head
    100008: char_ethan_Head1
    100010: char_ethan_Hips
    100012: char_ethan_Jaw
    100014: char_ethan_LeftArm
    100016: char_ethan_LeftBlink
    100018: char_ethan_LeftBrow
    100020: char_ethan_LeftCorner
    100022: char_ethan_LeftEye
    100024: char_ethan_LeftFoot
    100026: char_ethan_LeftForeArm
    100028: char_ethan_LeftHand
    100030: char_ethan_LeftHandIndex1
    100032: char_ethan_LeftHandIndex2
    100034: char_ethan_LeftHandIndex3
    100036: char_ethan_LeftHandIndex4
    100038: char_ethan_LeftHandMiddle1
    100040: char_ethan_LeftHandMiddle2
    100042: char_ethan_LeftHandMiddle3
    100044: char_ethan_LeftHandMiddle4
    100046: char_ethan_LeftHandPinky1
    100048: char_ethan_LeftHandPinky2
    100050: char_ethan_LeftHandPinky3
    100052: char_ethan_LeftHandPinky4
    100054: char_ethan_LeftHandRing1
    100056: char_ethan_LeftHandRing2
    100058: char_ethan_LeftHandRing3
    100060: char_ethan_LeftHandRing4
    100062: char_ethan_LeftHandThumb1
    100064: char_ethan_LeftHandThumb2
    100066: char_ethan_LeftHandThumb3
    100068: char_ethan_LeftHandThumb4
    100070: char_ethan_LeftLeg
    100072: char_ethan_LeftLowerLip
    100074: char_ethan_LeftShoulder
    100076: char_ethan_LeftToe1
    100078: char_ethan_LeftToe2
    100080: char_ethan_LeftUpLeg
    100082: char_ethan_LeftUpperLip
    100084: char_ethan_LowerLip
    100086: char_ethan_Neck
    100088: char_ethan_RightArm
    100090: char_ethan_RightBlink
    100092: char_ethan_RightBrow
    100094: char_ethan_RightCorner
    100096: char_ethan_RightEye
    100098: char_ethan_RightFoot
    100100: char_ethan_RightForeArm
    100102: char_ethan_RightHand
    100104: char_ethan_RightHandIndex1
    100106: char_ethan_RightHandIndex2
    100108: char_ethan_RightHandIndex3
    100110: char_ethan_RightHandIndex4
    100112: char_ethan_RightHandMiddle1
    100114: char_ethan_RightHandMiddle2
    100116: char_ethan_RightHandMiddle3
    100118: char_ethan_RightHandMiddle4
    100120: char_ethan_RightHandPinky1
    100122: char_ethan_RightHandPinky2
    100124: char_ethan_RightHandPinky3
    100126: char_ethan_RightHandPinky4
    100128: char_ethan_RightHandRing1
    100130: char_ethan_RightHandRing2
    100132: char_ethan_RightHandRing3
    100134: char_ethan_RightHandRing4
    100136: char_ethan_RightHandThumb1
    100138: char_ethan_RightHandThumb2
    100140: char_ethan_RightHandThumb3
    100142: char_ethan_RightHandThumb4
    100144: char_ethan_RightLeg
    100146: char_ethan_RightLowerLip
    100148: char_ethan_RightShoulder
    100150: char_ethan_RightToe1
    100152: char_ethan_RightToe2
    100154: char_ethan_RightUpLeg
    100156: char_ethan_RightUpperLip
    100158: char_ethan_skeleton
    100160: char_ethan_Spine
    100162: char_ethan_Spine1
    100164: char_ethan_Spine2
    100166: char_ethan_UpperLip
    100168: //RootNode
    100170: EthanBody
    100172: EthanGlasses
    100174: EthanHead
    100176: EthanHead1
    100178: EthanHips
    100180: EthanJaw
    100182: EthanLeftArm
    100184: EthanLeftBlink
    100186: EthanLeftBrow
    100188: EthanLeftCorner
    100190: EthanLeftEye
    100192: EthanLeftFoot
    100194: EthanLeftForeArm
    100196: EthanLeftHand
    100198: EthanLeftHandIndex1
    100200: EthanLeftHandIndex2
    100202: EthanLeftHandIndex3
    100204: EthanLeftHandIndex4
    100206: EthanLeftHandMiddle1
    100208: EthanLeftHandMiddle2
    100210: EthanLeftHandMiddle3
    100212: EthanLeftHandMiddle4
    100214: EthanLeftHandPinky1
    100216: EthanLeftHandPinky2
    100218: EthanLeftHandPinky3
    100220: EthanLeftHandPinky4
    100222: EthanLeftHandRing1
    100224: EthanLeftHandRing2
    100226: EthanLeftHandRing3
    100228: EthanLeftHandRing4
    100230: EthanLeftHandThumb1
    100232: EthanLeftHandThumb2
    100234: EthanLeftHandThumb3
    100236: EthanLeftHandThumb4
    100238: EthanLeftLeg
    100240: EthanLeftLowerLip
    100242: EthanLeftShoulder
    100244: EthanLeftToe1
    100246: EthanLeftToe2
    100248: EthanLeftUpLeg
    100250: EthanLeftUpperLip
    100252: EthanLowerLip
    100254: EthanNeck
    100256: EthanRightArm
    100258: EthanRightBlink
    100260: EthanRightBrow
    100262: EthanRightCorner
    100264: EthanRightEye
    100266: EthanRightFoot
    100268: EthanRightForeArm
    100270: EthanRightHand
    100272: EthanRightHandIndex1
    100274: EthanRightHandIndex2
    100276: EthanRightHandIndex3
    100278: EthanRightHandIndex4
    100280: EthanRightHandMiddle1
    100282: EthanRightHandMiddle2
    100284: EthanRightHandMiddle3
    100286: EthanRightHandMiddle4
    100288: EthanRightHandPinky1
    100290: EthanRightHandPinky2
    100292: EthanRightHandPinky3
    100294: EthanRightHandPinky4
    100296: EthanRightHandRing1
    100298: EthanRightHandRing2
    100300: EthanRightHandRing3
    100302: EthanRightHandRing4
    100304: EthanRightHandThumb1
    100306: EthanRightHandThumb2
    100308: EthanRightHandThumb3
    100310: EthanRightHandThumb4
    100312: EthanRightLeg
    100314: EthanRightLowerLip
    100316: EthanRightShoulder
    100318: EthanRightToe1
    100320: EthanRightToe2
    100322: EthanRightUpLeg
    100324: EthanRightUpperLip
    100326: EthanSkeleton
    100328: EthanSpine
    100330: EthanSpine1
    100332: EthanSpine2
    100334: EthanUpperLip
    100336: EthanBody1
    100338: EthanSkeleton1
    400000: char_cyberKid_Badge
    400002: char_ethan_body
    400004: char_ethan_glasses
    400006: char_ethan_Head
    400008: char_ethan_Head1
    400010: char_ethan_Hips
    400012: char_ethan_Jaw
    400014: char_ethan_LeftArm
    400016: char_ethan_LeftBlink
    400018: char_ethan_LeftBrow
    400020: char_ethan_LeftCorner
    400022: char_ethan_LeftEye
    400024: char_ethan_LeftFoot
    400026: char_ethan_LeftForeArm
    400028: char_ethan_LeftHand
    400030: char_ethan_LeftHandIndex1
    400032: char_ethan_LeftHandIndex2
    400034: char_ethan_LeftHandIndex3
    400036: char_ethan_LeftHandIndex4
    400038: char_ethan_LeftHandMiddle1
    400040: char_ethan_LeftHandMiddle2
    400042: char_ethan_LeftHandMiddle3
    400044: char_ethan_LeftHandMiddle4
    400046: char_ethan_LeftHandPinky1
    400048: char_ethan_LeftHandPinky2
    400050: char_ethan_LeftHandPinky3
    400052: char_ethan_LeftHandPinky4
    400054: char_ethan_LeftHandRing1
    400056: char_ethan_LeftHandRing2
    400058: char_ethan_LeftHandRing3
    400060: char_ethan_LeftHandRing4
    400062: char_ethan_LeftHandThumb1
    400064: char_ethan_LeftHandThumb2
    400066: char_ethan_LeftHandThumb3
    400068: char_ethan_LeftHandThumb4
    400070: char_ethan_LeftLeg
    400072: char_ethan_LeftLowerLip
    400074: char_ethan_LeftShoulder
    400076: char_ethan_LeftToe1
    400078: char_ethan_LeftToe2
    400080: char_ethan_LeftUpLeg
    400082: char_ethan_LeftUpperLip
    400084: char_ethan_LowerLip
    400086: char_ethan_Neck
    400088: char_ethan_RightArm
    400090: char_ethan_RightBlink
    400092: char_ethan_RightBrow
    400094: char_ethan_RightCorner
    400096: char_ethan_RightEye
    400098: char_ethan_RightFoot
    400100: char_ethan_RightForeArm
    400102: char_ethan_RightHand
    400104: char_ethan_RightHandIndex1
    400106: char_ethan_RightHandIndex2
    400108: char_ethan_RightHandIndex3
    400110: char_ethan_RightHandIndex4
    400112: char_ethan_RightHandMiddle1
    400114: char_ethan_RightHandMiddle2
    400116: char_ethan_RightHandMiddle3
    400118: char_ethan_RightHandMiddle4
    400120: char_ethan_RightHandPinky1
    400122: char_ethan_RightHandPinky2
    400124: char_ethan_RightHandPinky3
    400126: char_ethan_RightHandPinky4
    400128: char_ethan_RightHandRing1
    400130: char_ethan_RightHandRing2
    400132: char_ethan_RightHandRing3
    400134: char_ethan_RightHandRing4
    400136: char_ethan_RightHandThumb1
    400138: char_ethan_RightHandThumb2
    400140: char_ethan_RightHandThumb3
    400142: char_ethan_RightHandThumb4
    400144: char_ethan_RightLeg
    400146: char_ethan_RightLowerLip
    400148: char_ethan_RightShoulder
    400150: char_ethan_RightToe1
    400152: char_ethan_RightToe2
    400154: char_ethan_RightUpLeg
    400156: char_ethan_RightUpperLip
    400158: char_ethan_skeleton
    400160: char_ethan_Spine
    400162: char_ethan_Spine1
    400164: char_ethan_Spine2
    400166: char_ethan_UpperLip
    400168: //RootNode
    400170: EthanBody
    400172: EthanGlasses
    400174: EthanHead
    400176: EthanHead1
    400178: EthanHips
    400180: EthanJaw
    400182: EthanLeftArm
    400184: EthanLeftBlink
    400186: EthanLeftBrow
    400188: EthanLeftCorner
    400190: EthanLeftEye
    400192: EthanLeftFoot
    400194: EthanLeftForeArm
    400196: EthanLeftHand
    400198: EthanLeftHandIndex1
    400200: EthanLeftHandIndex2
    400202: EthanLeftHandIndex3
    400204: EthanLeftHandIndex4
    400206: EthanLeftHandMiddle1
    400208: EthanLeftHandMiddle2
    400210: EthanLeftHandMiddle3
    400212: EthanLeftHandMiddle4
    400214: EthanLeftHandPinky1
    400216: EthanLeftHandPinky2
    400218: EthanLeftHandPinky3
    400220: EthanLeftHandPinky4
    400222: EthanLeftHandRing1
    400224: EthanLeftHandRing2
    400226: EthanLeftHandRing3
    400228: EthanLeftHandRing4
    400230: EthanLeftHandThumb1
    400232: EthanLeftHandThumb2
    400234: EthanLeftHandThumb3
    400236: EthanLeftHandThumb4
    400238: EthanLeftLeg
    400240: EthanLeftLowerLip
    400242: EthanLeftShoulder
    400244: EthanLeftToe1
    400246: EthanLeftToe2
    400248: EthanLeftUpLeg
    400250: EthanLeftUpperLip
    400252: EthanLowerLip
    400254: EthanNeck
    400256: EthanRightArm
    400258: EthanRightBlink
    400260: EthanRightBrow
    400262: EthanRightCorner
    400264: EthanRightEye
    400266: EthanRightFoot
    400268: EthanRightForeArm
    400270: EthanRightHand
    400272: EthanRightHandIndex1
    400274: EthanRightHandIndex2
    400276: EthanRightHandIndex3
    400278: EthanRightHandIndex4
    400280: EthanRightHandMiddle1
    400282: EthanRightHandMiddle2
    400284: EthanRightHandMiddle3
    400286: EthanRightHandMiddle4
    400288: EthanRightHandPinky1
    400290: EthanRightHandPinky2
    400292: EthanRightHandPinky3
    400294: EthanRightHandPinky4
    400296: EthanRightHandRing1
    400298: EthanRightHandRing2
    400300: EthanRightHandRing3
    400302: EthanRightHandRing4
    400304: EthanRightHandThumb1
    400306: EthanRightHandThumb2
    400308: EthanRightHandThumb3
    400310: EthanRightHandThumb4
    400312: EthanRightLeg
    400314: EthanRightLowerLip
    400316: EthanRightShoulder
    400318: EthanRightToe1
    400320: EthanRightToe2
    400322: EthanRightUpLeg
    400324: EthanRightUpperLip
    400326: EthanSkeleton
    400328: EthanSpine
    400330: EthanSpine1
    400332: EthanSpine2
    400334: EthanUpperLip
    400336: EthanBody1
    400338: EthanSkeleton1
    4300000: char_ethan_glasses
    4300002: char_ethan_body
    4300004: EthanGlasses
    4300006: EthanBody
    4300008: EthanBody1
    7400000: Take 001
    9500000: //RootNode
    13700000: char_ethan_body
    13700002: char_ethan_glasses
    13700004: EthanBody
    13700006: EthanGlasses
    13700008: EthanBody1
  materials:
    importMaterials: 1
    materialName: 1
    materialSearch: 2
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.01
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 0
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human:
    - boneName: EthanHips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightToe1
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanSpine1
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanSpine2
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanHead
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanJaw
      humanName: Jaw
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightEye
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftEye
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftToe1
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanLeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: EthanRightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Ethan(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanBody
      parentName: Ethan(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanGlasses
      parentName: Ethan(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanSkeleton
      parentName: Ethan(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanHips
      parentName: EthanSkeleton
      position: {x: 0.00000042162256, y: 0.7787106, z: -0.033025585}
      rotation: {x: -0.49999934, y: 0.49999934, z: -0.50000066, w: 0.50000066}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanSpine
      parentName: EthanHips
      position: {x: -0.044983033, y: 0.00011812973, z: -0.000000026061407}
      rotation: {x: -0.0000020492112, y: 0.0000006409474, z: 0.043222737, w: 0.99906546}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftUpLeg
      parentName: EthanSpine
      position: {x: 0.044804916, y: -0.00400244, z: -0.07436281}
      rotation: {x: 0.009854246, y: 0.9996559, z: -0.0031334888, w: 0.024108019}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftLeg
      parentName: EthanLeftUpLeg
      position: {x: -0.3529785, y: -0.047479518, z: 0.03461981}
      rotation: {x: 0, y: 0, z: 0.16876774, w: 0.98565584}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftFoot
      parentName: EthanLeftLeg
      position: {x: -0.2827789, y: 0.1718791, z: 0.031038838}
      rotation: {x: -0.08430487, y: 0.035964992, z: -0.13311164, w: 0.9868539}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftToe1
      parentName: EthanLeftFoot
      position: {x: -0.085517354, y: -0.11005712, z: -0.0000006698085}
      rotation: {x: 7.3885096e-18, y: -8.589314e-17, z: -0.7071068, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftToe2
      parentName: EthanLeftToe1
      position: {x: 0.08174267, y: -0.000000009601407, z: -0.00000029316797}
      rotation: {x: 1, y: -5.6351963e-23, z: 1.110223e-16, w: -0.00000068451953}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightUpLeg
      parentName: EthanSpine
      position: {x: 0.044804532, y: -0.004002823, z: 0.07436302}
      rotation: {x: 0.0098541705, y: 0.9996559, z: 0.0031362663, w: -0.024108129}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightLeg
      parentName: EthanRightUpLeg
      position: {x: -0.3529783, y: -0.04747951, z: -0.034621846}
      rotation: {x: 0, y: 0, z: 0.16876684, w: 0.985656}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightFoot
      parentName: EthanRightLeg
      position: {x: -0.282779, y: 0.17187855, z: -0.031040668}
      rotation: {x: 0.08430424, y: -0.03596484, z: -0.13311061, w: 0.9868541}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightToe1
      parentName: EthanRightFoot
      position: {x: -0.08551728, y: -0.11005718, z: 0.000000062032285}
      rotation: {x: -1.3899192e-17, y: 3.352535e-17, z: -0.7071068, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightToe2
      parentName: EthanRightToe1
      position: {x: 0.08174264, y: 0.000000049485, z: 0.00000019218783}
      rotation: {x: -1.6724651e-16, y: 4.9321447e-33, z: -2.949027e-17, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanSpine1
      parentName: EthanSpine
      position: {x: -0.14667831, y: 0.025727088, z: -0.0000003282363}
      rotation: {x: -1.0587912e-22, y: -7.34684e-40, z: 6.938894e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanSpine2
      parentName: EthanSpine1
      position: {x: -0.12695539, y: 0.022281736, z: -0.0000002830808}
      rotation: {x: -1.0587912e-22, y: -7.34684e-40, z: 6.938894e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanNeck
      parentName: EthanSpine2
      position: {x: -0.12697862, y: 0.02224573, z: -0.00000028315998}
      rotation: {x: -7.318979e-17, y: 1.4170987e-17, z: -0.13052638, w: 0.9914448}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanHead
      parentName: EthanNeck
      position: {x: -0.090649985, y: -0.041122116, z: 0.000000058975164}
      rotation: {x: 1.3487235e-18, y: -1.7102821e-17, z: 0.087552026, w: 0.99616}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanHead1
      parentName: EthanHead
      position: {x: -0.17475267, y: 0.00000045072835, z: 0.00000009431633}
      rotation: {x: 1.0587912e-22, y: 4.646893e-23, z: -2.7755576e-17, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftCorner
      parentName: EthanHead1
      position: {x: 0.14939941, y: -0.06769698, z: -0.024466591}
      rotation: {x: -0.5, y: 0.5, z: 0.5000007, w: -0.49999928}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftUpperLip
      parentName: EthanHead1
      position: {x: 0.14274777, y: -0.075114705, z: -0.014378637}
      rotation: {x: -0.5, y: 0.5, z: 0.5000007, w: -0.49999928}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightCorner
      parentName: EthanHead1
      position: {x: 0.14939931, y: -0.06769704, z: 0.02431402}
      rotation: {x: -0.5, y: 0.5, z: 0.5000007, w: -0.49999928}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightUpperLip
      parentName: EthanHead1
      position: {x: 0.14274772, y: -0.07511473, z: 0.014226249}
      rotation: {x: -0.5, y: 0.5, z: 0.5000007, w: -0.49999928}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanUpperLip
      parentName: EthanHead1
      position: {x: 0.14274773, y: -0.087347545, z: -0.00007465846}
      rotation: {x: -0.5, y: 0.5, z: 0.5000007, w: -0.49999928}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanJaw
      parentName: EthanHead
      position: {x: -0.03240739, y: -0.029785074, z: -0.00000007152633}
      rotation: {x: -0.49673736, y: 0.5032417, z: 0.5032421, w: -0.49673653}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftLowerLip
      parentName: EthanJaw
      position: {x: 0.044976283, y: -0.009381128, z: -0.014150948}
      rotation: {x: 5.54211e-23, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLowerLip
      parentName: EthanJaw
      position: {x: 0.057204966, y: 0.0049228696, z: -0.014469165}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightLowerLip
      parentName: EthanJaw
      position: {x: 0.044976283, y: 0.019223755, z: -0.014150911}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftBlink
      parentName: EthanHead
      position: {x: -0.08582658, y: -0.07248985, z: -0.023909489}
      rotation: {x: -0.43938157, y: 0.5540251, z: 0.55402577, w: -0.4393808}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftBrow
      parentName: EthanHead
      position: {x: -0.09988994, y: -0.08062434, z: -0.028973255}
      rotation: {x: -0.5, y: 0.5, z: 0.5000007, w: -0.49999928}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftEye
      parentName: EthanHead
      position: {x: -0.0817658, y: -0.058895037, z: -0.027851813}
      rotation: {x: -0.5, y: 0.5, z: 0.5000007, w: -0.49999928}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightBlink
      parentName: EthanHead
      position: {x: -0.08582671, y: -0.07248995, z: 0.033750787}
      rotation: {x: -0.44628233, y: 0.55759704, z: 0.54723495, w: -0.43640757}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightBrow
      parentName: EthanHead
      position: {x: -0.09989007, y: -0.08062444, z: 0.02868702}
      rotation: {x: -0.5, y: 0.5, z: 0.5000007, w: -0.49999928}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightEye
      parentName: EthanHead
      position: {x: -0.0817396, y: -0.05880309, z: 0.027845075}
      rotation: {x: -0.5, y: 0.5, z: 0.5000007, w: -0.49999928}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftShoulder
      parentName: EthanNeck
      position: {x: 0.00002230769, y: -0.00006137982, z: -0.026027031}
      rotation: {x: -0.48608947, y: -0.0722202, z: 0.8640734, w: -0.10898846}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftArm
      parentName: EthanLeftShoulder
      position: {x: 0.08180639, y: 0.009300345, z: -0.09634663}
      rotation: {x: 0.06292434, y: -0.29547754, z: -0.011793284, w: 0.95320225}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftForeArm
      parentName: EthanLeftArm
      position: {x: 0.08794441, y: -0.014121806, z: -0.22941957}
      rotation: {x: -0.18687433, y: 0.0007973312, z: 0.14583053, w: 0.97149926}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHand
      parentName: EthanLeftForeArm
      position: {x: 0.070799686, y: 0.035503525, z: -0.14085448}
      rotation: {x: -0.70413613, y: -0.061654028, z: -0.061605077, w: 0.7046956}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandIndex1
      parentName: EthanLeftHand
      position: {x: 0.018482484, y: 0.08044448, z: 0.051040206}
      rotation: {x: 0.0015112518, y: -0.043594275, z: -0.043560736, w: 0.9980981}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandIndex2
      parentName: EthanLeftHandIndex1
      position: {x: 0.00022275507, y: 0.025879132, z: 0.010385311}
      rotation: {x: -0.052662343, y: 0.035397545, z: -0.2640296, w: 0.9624251}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandIndex3
      parentName: EthanLeftHandIndex2
      position: {x: -0.012793984, y: 0.016905012, z: 0.011081521}
      rotation: {x: -0.02921515, y: 0.08614237, z: -0.2737419, w: 0.9574922}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandIndex4
      parentName: EthanLeftHandIndex3
      position: {x: -0.020055601, y: 0.0045873905, z: 0.007730915}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandMiddle1
      parentName: EthanLeftHand
      position: {x: 0.013394796, y: 0.084405266, z: 0.03165157}
      rotation: {x: -0.044011682, y: 2.8034074e-17, z: -5.7106006e-18, w: 0.999031}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandMiddle2
      parentName: EthanLeftHandMiddle1
      position: {x: 0.0037795054, y: 0.030145722, z: 0.0116361305}
      rotation: {x: -0.091909915, y: 0.05467633, z: -0.42002133, w: 0.90119106}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandMiddle3
      parentName: EthanLeftHandMiddle2
      position: {x: -0.017910194, y: 0.014384004, z: 0.013012275}
      rotation: {x: -0.040044915, y: 0.008001845, z: -0.18298166, w: 0.9822679}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandMiddle4
      parentName: EthanLeftHandMiddle3
      position: {x: -0.020049915, y: 0.0035380095, z: 0.012281134}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandPinky1
      parentName: EthanLeftHand
      position: {x: -0.0067506824, y: 0.09838339, z: -0.0051224125}
      rotation: {x: -0.15040894, y: 0.08822873, z: -0.33158392, w: 0.92717046}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandPinky2
      parentName: EthanLeftHandPinky1
      position: {x: -0.010750259, y: 0.009622569, z: 0.009467701}
      rotation: {x: -0.044671368, y: 0.009720898, z: -0.17979467, w: 0.9826413}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandPinky3
      parentName: EthanLeftHandPinky2
      position: {x: -0.0155973025, y: 0.0034428125, z: 0.0112945065}
      rotation: {x: -0.045708735, y: -0.0008739185, z: -0.17979562, w: 0.98264116}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandPinky4
      parentName: EthanLeftHandPinky3
      position: {x: -0.009463298, y: -0.002946482, z: 0.006961719}
      rotation: {x: 8.326673e-17, y: 1.3877788e-17, z: -6.938894e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandRing1
      parentName: EthanLeftHand
      position: {x: 0.011634815, y: 0.097086444, z: 0.00849549}
      rotation: {x: -0.04750495, y: -0.02680468, z: -0.21266928, w: 0.9756006}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandRing2
      parentName: EthanLeftHandRing1
      position: {x: -0.009746879, y: 0.022168977, z: 0.013826583}
      rotation: {x: -0.06670461, y: 0.026029166, z: -0.25883004, w: 0.9632653}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandRing3
      parentName: EthanLeftHandRing2
      position: {x: -0.015402557, y: 0.007800675, z: 0.011749413}
      rotation: {x: -0.01883299, y: 0.002628695, z: -0.09988077, w: 0.9948177}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandRing4
      parentName: EthanLeftHandRing3
      position: {x: -0.01707594, y: 0.0025099975, z: 0.012105394}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandThumb1
      parentName: EthanLeftHand
      position: {x: -0.004633863, y: 0.028035661, z: 0.048953336}
      rotation: {x: 0.66034335, y: -0.2809301, z: -0.23108189, w: 0.6569825}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandThumb2
      parentName: EthanLeftHandThumb1
      position: {x: -0.0031383773, y: 0.025515735, z: -0.014984593}
      rotation: {x: 0.042991735, y: -0.025952656, z: -0.17635393, w: 0.98304504}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandThumb3
      parentName: EthanLeftHandThumb2
      position: {x: -0.010219031, y: 0.014567392, z: -0.012677365}
      rotation: {x: 0.04783477, y: -0.015307045, z: -0.17635551, w: 0.9830444}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanLeftHandThumb4
      parentName: EthanLeftHandThumb3
      position: {x: -0.016609639, y: 0.007850524, z: -0.014726918}
      rotation: {x: 2.220446e-16, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightShoulder
      parentName: EthanNeck
      position: {x: 0.000022271464, y: -0.00006150465, z: 0.026027026}
      rotation: {x: 0.48609108, y: 0.07221761, z: 0.86407256, w: -0.10898973}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightArm
      parentName: EthanRightShoulder
      position: {x: 0.081806876, y: 0.009300272, z: 0.096346125}
      rotation: {x: -0.06292409, y: 0.2954787, z: -0.011792956, w: 0.9532019}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightForeArm
      parentName: EthanRightArm
      position: {x: 0.087945335, y: -0.014121898, z: 0.22941919}
      rotation: {x: 0.1868743, y: -0.00079789746, z: 0.1458294, w: 0.9714995}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHand
      parentName: EthanRightForeArm
      position: {x: 0.07080024, y: 0.03550319, z: 0.14085418}
      rotation: {x: 0.70413506, y: 0.061653953, z: -0.061604813, w: 0.7046967}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandIndex1
      parentName: EthanRightHand
      position: {x: 0.018482815, y: 0.08044467, z: -0.05103978}
      rotation: {x: -0.0015103403, y: 0.04359434, z: -0.04356065, w: 0.9980981}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandIndex2
      parentName: EthanRightHandIndex1
      position: {x: 0.0002228482, y: 0.025879227, z: -0.0103851855}
      rotation: {x: 0.05266336, y: -0.035397556, z: -0.2640311, w: 0.9624247}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandIndex3
      parentName: EthanRightHandIndex2
      position: {x: -0.012793941, y: 0.01690497, z: -0.01108144}
      rotation: {x: 0.029214451, y: -0.08614219, z: -0.27374247, w: 0.9574922}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandIndex4
      parentName: EthanRightHandIndex3
      position: {x: -0.020055542, y: 0.0045875087, z: -0.007730851}
      rotation: {x: 0.48288566, y: 0, z: -0, w: 0.8756834}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandMiddle1
      parentName: EthanRightHand
      position: {x: 0.013395289, y: 0.08440544, z: -0.031651095}
      rotation: {x: 0.044012643, y: 6.003835e-17, z: 1.01539365e-16, w: 0.999031}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandMiddle2
      parentName: EthanRightHandMiddle1
      position: {x: 0.0037796986, y: 0.030145705, z: -0.011636003}
      rotation: {x: 0.094235055, y: -0.05927117, z: -0.41746008, w: 0.9018502}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandMiddle3
      parentName: EthanRightHandMiddle2
      position: {x: -0.017910058, y: 0.01438411, z: -0.013012125}
      rotation: {x: 0.040047344, y: -0.008001346, z: -0.18298474, w: 0.9822672}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandMiddle4
      parentName: EthanRightHandMiddle3
      position: {x: -0.020049982, y: 0.003538114, z: -0.012281116}
      rotation: {x: 0.37079856, y: 0, z: -0, w: 0.9287133}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandPinky1
      parentName: EthanRightHand
      position: {x: -0.006750106, y: 0.09838347, z: 0.005122984}
      rotation: {x: 0.14634132, y: -0.0853054, z: -0.3388892, w: 0.92545193}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandPinky2
      parentName: EthanRightHandPinky1
      position: {x: -0.010876534, y: 0.00954607, z: -0.009400739}
      rotation: {x: 0.035175707, y: -0.014915071, z: -0.15348981, w: 0.98741126}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandPinky3
      parentName: EthanRightHandPinky2
      position: {x: -0.015650306, y: 0.004325165, z: -0.010911368}
      rotation: {x: 0.04428908, y: -0.00024435105, z: -0.18078858, w: 0.9825242}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandPinky4
      parentName: EthanRightHandPinky3
      position: {x: -0.009746457, y: -0.002438951, z: -0.006764462}
      rotation: {x: 0.1938353, y: 4.4878757e-17, z: 1.2351859e-17, w: 0.9810341}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandRing1
      parentName: EthanRightHand
      position: {x: 0.011635365, y: 0.09708646, z: -0.008494926}
      rotation: {x: 0.06069251, y: 0.019897, z: -0.24453726, w: 0.96753407}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandRing2
      parentName: EthanRightHandRing1
      position: {x: -0.011261935, y: 0.021090085, z: -0.014353217}
      rotation: {x: 0.06872708, y: -0.023836246, z: -0.2580223, w: 0.9633966}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandRing3
      parentName: EthanRightHandRing2
      position: {x: -0.015814971, y: 0.0064337812, z: -0.012026324}
      rotation: {x: 0.019224158, y: -0.0019391933, z: -0.09966884, w: 0.99483305}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandRing4
      parentName: EthanRightHandRing3
      position: {x: -0.017074374, y: 0.0010487483, z: -0.012320689}
      rotation: {x: 0.4587139, y: 0, z: -0, w: 0.888584}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandThumb1
      parentName: EthanRightHand
      position: {x: -0.0046337163, y: 0.028035993, z: -0.04895318}
      rotation: {x: -0.660343, y: 0.28093126, z: -0.23108025, w: 0.656983}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandThumb2
      parentName: EthanRightHandThumb1
      position: {x: -0.0031383634, y: 0.025515651, z: 0.014984788}
      rotation: {x: -0.04299499, y: 0.025953224, z: -0.17635447, w: 0.9830448}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandThumb3
      parentName: EthanRightHandThumb2
      position: {x: -0.010218882, y: 0.014567167, z: 0.012677433}
      rotation: {x: -0.04783391, y: 0.015310023, z: -0.17635584, w: 0.9830443}
      scale: {x: 1, y: 1, z: 1}
    - name: EthanRightHandThumb4
      parentName: EthanRightHandThumb3
      position: {x: -0.016609553, y: 0.007850429, z: 0.0147271305}
      rotation: {x: -0.8654032, y: 0, z: 0, w: 0.5010762}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
