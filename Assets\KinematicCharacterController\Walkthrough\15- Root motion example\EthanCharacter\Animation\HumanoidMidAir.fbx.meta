fileFormatVersion: 2
guid: f03e10c73f30b4ab4aa8ea8f1cc16d36
ModelImporter:
  serializedVersion: 18
  fileIDToRecycleName:
    100000: //RootNode
    100002: l_hipProxy_geo
    100004: l_kneeProxy_geo
    100006: l_ankleProxy_geo
    100008: l_ballProxy_geo
    100010: LToeBase_End2
    100012: LeftToes
    100014: LeftFoot
    100016: LeftLeg
    100018: LeftUpLeg
    100020: pelvisProxy_geo
    100022: r_hipProxy_geo
    100024: r_kneeProxy_geo
    100026: r_ankleProxy_geo
    100028: r_ballProxy_geo
    100030: LToeBase_End3
    100032: RightToes
    100034: RightFoot
    100036: RightLeg
    100038: RightUpLeg
    100040: spineProxy_geo
    100042: l_clavicleProxy_geo
    100044: l_shourderProxy_geo
    100046: l_erbowProxy_geo
    100048: l_wristProxy_geo
    100050: l_thumbProxy_01_geo
    100052: l_thumbProxy_02_geo
    100054: l_thumbProxy_03_geo
    100056: LeftHandThumb13
    100058: LeftHandThumb3
    100060: LeftHandThumb2
    100062: LeftHandThumb1
    100064: l_indexProxy_01_geo
    100066: l_indexProxy_02_geo
    100068: l_indexProxy_03_geo
    100070: LeftHandIndex13
    100072: LeftHandIndex3
    100074: LeftHandIndex2
    100076: LeftHandIndex1
    100078: l_middleProxy_01_geo
    100080: l_middleProxy_02_geo
    100082: l_middleProxy_03_geo
    100084: LeftHandMiddle13
    100086: LeftHandMiddle3
    100088: LeftHandMiddle2
    100090: LeftHandMiddle1
    100092: l_ringProxy_01_geo
    100094: l_ringProxy_02_geo
    100096: l_ringProxy_03_geo
    100098: LeftHandRing13
    100100: LeftHandRing3
    100102: LeftHandRing2
    100104: LeftHandRing1
    100106: l_pinkyProxy_01_geo
    100108: l_pinkyProxy_02_geo
    100110: l_pinkyProxy_03_geo
    100112: LeftHandPinky13
    100114: LeftHandPinky3
    100116: LeftHandPinky2
    100118: LeftHandPinky1
    100120: LeftHand
    100122: LeftForeArm
    100124: LeftArm
    100126: LeftShoulder
    100128: chestProxy_geo
    100130: r_clavicleProxy_geo
    100132: r_shourderProxy_geo
    100134: r_erbowProxy_geo
    100136: r_wristProxy_geo
    100138: r_thumbProxy_01_geo
    100140: r_thumbProxy_02_geo
    100142: r_thumbProxy_03_geo
    100144: LeftHandThumb17
    100146: RightHandThumb3
    100148: RightHandThumb2
    100150: RightHandThumb1
    100152: r_indexProxy_01_geo
    100154: r_indexProxy_02_geo
    100156: r_indexProxy_03_geo
    100158: LeftHandIndex17
    100160: RightHandIndex3
    100162: RightHandIndex2
    100164: RightHandIndex1
    100166: r_middleProxy_01_geo
    100168: r_middleProxy_02_geo
    100170: r_middleProxy_03_geo
    100172: LeftHandMiddle17
    100174: RightHandMiddle3
    100176: RightHandMiddle2
    100178: RightHandMiddle1
    100180: r_ringProxy_01_geo
    100182: r_ringProxy_02_geo
    100184: r_ringProxy_03_geo
    100186: LeftHandRing17
    100188: RightHandRing3
    100190: RightHandRing2
    100192: RightHandRing1
    100194: r_pinkyProxy_01_geo
    100196: r_pinkyProxy_02_geo
    100198: r_pinkyProxy_03_geo
    100200: LeftHandPinky17
    100202: RightHandPinky3
    100204: RightHandPinky2
    100206: RightHandPinky1
    100208: RightHand
    100210: RightForeArm
    100212: RightArm
    100214: RightShoulder
    100216: neckProxy_geo
    100218: UNI_01_Upper_teethProxy
    100220: headProxy_geo
    100222: RightLipUpper
    100224: RightNostril
    100226: RightCheek
    100228: RightEyelidLower
    100230: RightEyelidUpper
    100232: RightIOuterBrow
    100234: RightInnerBrow
    100236: LeftIOuterBrow
    100238: LeftInnerBrow
    100240: LeftEyelidUpper
    100242: LeftEyelidLower
    100244: LeftCheek
    100246: LeftNostril
    100248: LeftLipUpper
    100250: jawProxy_geo
    100252: UNI_01_Lower_teethProxy
    100254: LeftLipCorner
    100256: RightLipCorner
    100258: RightLipLower
    100260: JawEND
    100262: LeftLipLower
    100264: UNI_01_TongueTipProxy
    100266: TongueTip
    100268: UNI_01_TongueBaseProxy
    100270: TongueBack
    100272: Jaw
    100274: r_UNI_eye
    100276: RightEye
    100278: l_UNI_eye
    100280: LeftEye
    100282: HeadTop_End
    100284: Head
    100286: Neck
    100288: Chest
    100290: Spine
    100292: Hips
    100294: Reference
    400000: //RootNode
    400002: l_hipProxy_geo
    400004: l_kneeProxy_geo
    400006: l_ankleProxy_geo
    400008: l_ballProxy_geo
    400010: LToeBase_End2
    400012: LeftToes
    400014: LeftFoot
    400016: LeftLeg
    400018: LeftUpLeg
    400020: pelvisProxy_geo
    400022: r_hipProxy_geo
    400024: r_kneeProxy_geo
    400026: r_ankleProxy_geo
    400028: r_ballProxy_geo
    400030: LToeBase_End3
    400032: RightToes
    400034: RightFoot
    400036: RightLeg
    400038: RightUpLeg
    400040: spineProxy_geo
    400042: l_clavicleProxy_geo
    400044: l_shourderProxy_geo
    400046: l_erbowProxy_geo
    400048: l_wristProxy_geo
    400050: l_thumbProxy_01_geo
    400052: l_thumbProxy_02_geo
    400054: l_thumbProxy_03_geo
    400056: LeftHandThumb13
    400058: LeftHandThumb3
    400060: LeftHandThumb2
    400062: LeftHandThumb1
    400064: l_indexProxy_01_geo
    400066: l_indexProxy_02_geo
    400068: l_indexProxy_03_geo
    400070: LeftHandIndex13
    400072: LeftHandIndex3
    400074: LeftHandIndex2
    400076: LeftHandIndex1
    400078: l_middleProxy_01_geo
    400080: l_middleProxy_02_geo
    400082: l_middleProxy_03_geo
    400084: LeftHandMiddle13
    400086: LeftHandMiddle3
    400088: LeftHandMiddle2
    400090: LeftHandMiddle1
    400092: l_ringProxy_01_geo
    400094: l_ringProxy_02_geo
    400096: l_ringProxy_03_geo
    400098: LeftHandRing13
    400100: LeftHandRing3
    400102: LeftHandRing2
    400104: LeftHandRing1
    400106: l_pinkyProxy_01_geo
    400108: l_pinkyProxy_02_geo
    400110: l_pinkyProxy_03_geo
    400112: LeftHandPinky13
    400114: LeftHandPinky3
    400116: LeftHandPinky2
    400118: LeftHandPinky1
    400120: LeftHand
    400122: LeftForeArm
    400124: LeftArm
    400126: LeftShoulder
    400128: chestProxy_geo
    400130: r_clavicleProxy_geo
    400132: r_shourderProxy_geo
    400134: r_erbowProxy_geo
    400136: r_wristProxy_geo
    400138: r_thumbProxy_01_geo
    400140: r_thumbProxy_02_geo
    400142: r_thumbProxy_03_geo
    400144: LeftHandThumb17
    400146: RightHandThumb3
    400148: RightHandThumb2
    400150: RightHandThumb1
    400152: r_indexProxy_01_geo
    400154: r_indexProxy_02_geo
    400156: r_indexProxy_03_geo
    400158: LeftHandIndex17
    400160: RightHandIndex3
    400162: RightHandIndex2
    400164: RightHandIndex1
    400166: r_middleProxy_01_geo
    400168: r_middleProxy_02_geo
    400170: r_middleProxy_03_geo
    400172: LeftHandMiddle17
    400174: RightHandMiddle3
    400176: RightHandMiddle2
    400178: RightHandMiddle1
    400180: r_ringProxy_01_geo
    400182: r_ringProxy_02_geo
    400184: r_ringProxy_03_geo
    400186: LeftHandRing17
    400188: RightHandRing3
    400190: RightHandRing2
    400192: RightHandRing1
    400194: r_pinkyProxy_01_geo
    400196: r_pinkyProxy_02_geo
    400198: r_pinkyProxy_03_geo
    400200: LeftHandPinky17
    400202: RightHandPinky3
    400204: RightHandPinky2
    400206: RightHandPinky1
    400208: RightHand
    400210: RightForeArm
    400212: RightArm
    400214: RightShoulder
    400216: neckProxy_geo
    400218: UNI_01_Upper_teethProxy
    400220: headProxy_geo
    400222: RightLipUpper
    400224: RightNostril
    400226: RightCheek
    400228: RightEyelidLower
    400230: RightEyelidUpper
    400232: RightIOuterBrow
    400234: RightInnerBrow
    400236: LeftIOuterBrow
    400238: LeftInnerBrow
    400240: LeftEyelidUpper
    400242: LeftEyelidLower
    400244: LeftCheek
    400246: LeftNostril
    400248: LeftLipUpper
    400250: jawProxy_geo
    400252: UNI_01_Lower_teethProxy
    400254: LeftLipCorner
    400256: RightLipCorner
    400258: RightLipLower
    400260: JawEND
    400262: LeftLipLower
    400264: UNI_01_TongueTipProxy
    400266: TongueTip
    400268: UNI_01_TongueBaseProxy
    400270: TongueBack
    400272: Jaw
    400274: r_UNI_eye
    400276: RightEye
    400278: l_UNI_eye
    400280: LeftEye
    400282: HeadTop_End
    400284: Head
    400286: Neck
    400288: Chest
    400290: Spine
    400292: Hips
    400294: Reference
    2300000: l_hipProxy_geo
    2300002: l_kneeProxy_geo
    2300004: l_ankleProxy_geo
    2300006: l_ballProxy_geo
    2300008: pelvisProxy_geo
    2300010: r_hipProxy_geo
    2300012: r_kneeProxy_geo
    2300014: r_ankleProxy_geo
    2300016: r_ballProxy_geo
    2300018: spineProxy_geo
    2300020: l_clavicleProxy_geo
    2300022: l_shourderProxy_geo
    2300024: l_erbowProxy_geo
    2300026: l_wristProxy_geo
    2300028: l_thumbProxy_01_geo
    2300030: l_thumbProxy_02_geo
    2300032: l_thumbProxy_03_geo
    2300034: l_indexProxy_01_geo
    2300036: l_indexProxy_02_geo
    2300038: l_indexProxy_03_geo
    2300040: l_middleProxy_01_geo
    2300042: l_middleProxy_02_geo
    2300044: l_middleProxy_03_geo
    2300046: l_ringProxy_01_geo
    2300048: l_ringProxy_02_geo
    2300050: l_ringProxy_03_geo
    2300052: l_pinkyProxy_01_geo
    2300054: l_pinkyProxy_02_geo
    2300056: l_pinkyProxy_03_geo
    2300058: chestProxy_geo
    2300060: r_clavicleProxy_geo
    2300062: r_shourderProxy_geo
    2300064: r_erbowProxy_geo
    2300066: r_wristProxy_geo
    2300068: r_thumbProxy_01_geo
    2300070: r_thumbProxy_02_geo
    2300072: r_thumbProxy_03_geo
    2300074: r_indexProxy_01_geo
    2300076: r_indexProxy_02_geo
    2300078: r_indexProxy_03_geo
    2300080: r_middleProxy_01_geo
    2300082: r_middleProxy_02_geo
    2300084: r_middleProxy_03_geo
    2300086: r_ringProxy_01_geo
    2300088: r_ringProxy_02_geo
    2300090: r_ringProxy_03_geo
    2300092: r_pinkyProxy_01_geo
    2300094: r_pinkyProxy_02_geo
    2300096: r_pinkyProxy_03_geo
    2300098: neckProxy_geo
    2300100: UNI_01_Upper_teethProxy
    2300102: headProxy_geo
    2300104: jawProxy_geo
    2300106: UNI_01_Lower_teethProxy
    2300108: UNI_01_TongueTipProxy
    2300110: UNI_01_TongueBaseProxy
    2300112: r_UNI_eye
    2300114: l_UNI_eye
    3300000: l_hipProxy_geo
    3300002: l_kneeProxy_geo
    3300004: l_ankleProxy_geo
    3300006: l_ballProxy_geo
    3300008: pelvisProxy_geo
    3300010: r_hipProxy_geo
    3300012: r_kneeProxy_geo
    3300014: r_ankleProxy_geo
    3300016: r_ballProxy_geo
    3300018: spineProxy_geo
    3300020: l_clavicleProxy_geo
    3300022: l_shourderProxy_geo
    3300024: l_erbowProxy_geo
    3300026: l_wristProxy_geo
    3300028: l_thumbProxy_01_geo
    3300030: l_thumbProxy_02_geo
    3300032: l_thumbProxy_03_geo
    3300034: l_indexProxy_01_geo
    3300036: l_indexProxy_02_geo
    3300038: l_indexProxy_03_geo
    3300040: l_middleProxy_01_geo
    3300042: l_middleProxy_02_geo
    3300044: l_middleProxy_03_geo
    3300046: l_ringProxy_01_geo
    3300048: l_ringProxy_02_geo
    3300050: l_ringProxy_03_geo
    3300052: l_pinkyProxy_01_geo
    3300054: l_pinkyProxy_02_geo
    3300056: l_pinkyProxy_03_geo
    3300058: chestProxy_geo
    3300060: r_clavicleProxy_geo
    3300062: r_shourderProxy_geo
    3300064: r_erbowProxy_geo
    3300066: r_wristProxy_geo
    3300068: r_thumbProxy_01_geo
    3300070: r_thumbProxy_02_geo
    3300072: r_thumbProxy_03_geo
    3300074: r_indexProxy_01_geo
    3300076: r_indexProxy_02_geo
    3300078: r_indexProxy_03_geo
    3300080: r_middleProxy_01_geo
    3300082: r_middleProxy_02_geo
    3300084: r_middleProxy_03_geo
    3300086: r_ringProxy_01_geo
    3300088: r_ringProxy_02_geo
    3300090: r_ringProxy_03_geo
    3300092: r_pinkyProxy_01_geo
    3300094: r_pinkyProxy_02_geo
    3300096: r_pinkyProxy_03_geo
    3300098: neckProxy_geo
    3300100: UNI_01_Upper_teethProxy
    3300102: headProxy_geo
    3300104: jawProxy_geo
    3300106: UNI_01_Lower_teethProxy
    3300108: UNI_01_TongueTipProxy
    3300110: UNI_01_TongueBaseProxy
    3300112: r_UNI_eye
    3300114: l_UNI_eye
    4300000: l_UNI_eye
    4300002: r_UNI_eye
    4300004: UNI_01_TongueBaseProxy
    4300006: UNI_01_TongueTipProxy
    4300008: UNI_01_Lower_teethProxy
    4300010: jawProxy_geo
    4300012: headProxy_geo
    4300014: UNI_01_Upper_teethProxy
    4300016: neckProxy_geo
    4300018: r_pinkyProxy_03_geo
    4300020: r_pinkyProxy_02_geo
    4300022: r_pinkyProxy_01_geo
    4300024: r_ringProxy_03_geo
    4300026: r_ringProxy_02_geo
    4300028: r_ringProxy_01_geo
    4300030: r_middleProxy_03_geo
    4300032: r_middleProxy_02_geo
    4300034: r_middleProxy_01_geo
    4300036: r_indexProxy_03_geo
    4300038: r_indexProxy_02_geo
    4300040: r_indexProxy_01_geo
    4300042: r_thumbProxy_03_geo
    4300044: r_thumbProxy_02_geo
    4300046: r_thumbProxy_01_geo
    4300048: r_wristProxy_geo
    4300050: r_erbowProxy_geo
    4300052: r_shourderProxy_geo
    4300054: r_clavicleProxy_geo
    4300056: chestProxy_geo
    4300058: l_pinkyProxy_03_geo
    4300060: l_pinkyProxy_02_geo
    4300062: l_pinkyProxy_01_geo
    4300064: l_ringProxy_03_geo
    4300066: l_ringProxy_02_geo
    4300068: l_ringProxy_01_geo
    4300070: l_middleProxy_03_geo
    4300072: l_middleProxy_02_geo
    4300074: l_middleProxy_01_geo
    4300076: l_indexProxy_03_geo
    4300078: l_indexProxy_02_geo
    4300080: l_indexProxy_01_geo
    4300082: l_thumbProxy_03_geo
    4300084: l_thumbProxy_02_geo
    4300086: l_thumbProxy_01_geo
    4300088: l_wristProxy_geo
    4300090: l_erbowProxy_geo
    4300092: l_shourderProxy_geo
    4300094: l_clavicleProxy_geo
    4300096: spineProxy_geo
    4300098: r_ballProxy_geo
    4300100: r_ankleProxy_geo
    4300102: r_kneeProxy_geo
    4300104: r_hipProxy_geo
    4300106: pelvisProxy_geo
    4300108: l_ballProxy_geo
    4300110: l_ankleProxy_geo
    4300112: l_kneeProxy_geo
    4300114: l_hipProxy_geo
    7400000: __preview___209_Run_JumpDownLow_Run
    7400002: HumanoidMidAirRight
    7400004: HumanoidMidAirLeft
    9500000: //RootNode
    11100000: //RootNode
  materials:
    importMaterials: 0
    materialName: 1
    materialSearch: 2
  animations:
    legacyGenerateAnimations: 3
    bakeSimulation: 0
    optimizeGameObjects: 0
    motionNodeName: 
    pivotNodeName: 
    animationCompression: 3
    animationRotationError: .5
    animationPositionError: .5
    animationScaleError: .5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: HumanoidMidAirRight
      takeName: _209_Run_JumpDownLow_Run
      firstFrame: 118
      lastFrame: 120
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 1
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
    - serializedVersion: 16
      name: HumanoidMidAirLeft
      takeName: _209_Run_JumpDownLow_Run
      firstFrame: 118
      lastFrame: 120
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 1
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Hips
        weight: 1
      - path: Hips/Spine
        weight: 1
      - path: Hips/Spine/Chest
        weight: 1
      - path: Hips/Spine/Chest/Neck
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/HeadTop_End
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/LeftEye
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/RightEye
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/Jaw
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/Jaw/TongueBack
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/Jaw/TongueTip
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/Jaw/LeftLipLower
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/Jaw/JawEND
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/Jaw/RightLipLower
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/Jaw/RightLipCorner
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/Jaw/LeftLipCorner
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/LeftLipUpper
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/LeftNostril
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/LeftCheek
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/LeftEyelidLower
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/LeftEyelidUpper
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/LeftInnerBrow
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/LeftIOuterBrow
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/RightInnerBrow
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/RightIOuterBrow
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/RightEyelidUpper
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/RightEyelidLower
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/RightCheek
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/RightNostril
        weight: 1
      - path: Hips/Spine/Chest/Neck/Head/RightLipUpper
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3/LeftHandPinky17
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3/LeftHandRing17
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3/LeftHandMiddle17
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3/LeftHandIndex17
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3
        weight: 1
      - path: Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3/LeftHandThumb17
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3/LeftHandPinky13
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3/LeftHandRing13
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3/LeftHandMiddle13
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3/LeftHandIndex13
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3
        weight: 1
      - path: Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3/LeftHandThumb13
        weight: 1
      - path: Hips/RightUpLeg
        weight: 1
      - path: Hips/RightUpLeg/RightLeg
        weight: 1
      - path: Hips/RightUpLeg/RightLeg/RightFoot
        weight: 1
      - path: Hips/RightUpLeg/RightLeg/RightFoot/RightToes
        weight: 1
      - path: Hips/RightUpLeg/RightLeg/RightFoot/RightToes/LToeBase_End3
        weight: 1
      - path: Hips/LeftUpLeg
        weight: 1
      - path: Hips/LeftUpLeg/LeftLeg
        weight: 1
      - path: Hips/LeftUpLeg/LeftLeg/LeftFoot
        weight: 1
      - path: Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes
        weight: 1
      - path: Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes/LToeBase_End2
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: .00999999978
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    splitTangentsAcrossUV: 1
    normalImportMode: 0
    tangentImportMode: 1
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToes
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Jaw
      humanName: Jaw
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightEye
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftEye
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToes
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: HumanoidMidAir(Clone)
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Hips
      position: {x: 0, y: .978280783, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftUpLeg
      position: {x: -.0754494965, y: -.0456640199, z: 3.5527136e-17}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLeg
      position: {x: -.0205504987, y: -.409129977, z: -.000718647963}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftFoot
      position: {x: -.00515299942, y: -.423155904, z: -.0276488513}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftToes
      position: {x: -.00748699997, y: -.0731673017, z: .145427123}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LToeBase_End2
      position: {x: .0126400003, y: -.0131357787, z: .0358933695}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightUpLeg
      position: {x: .0754495338, y: -.0456639901, z: -7.1054272e-17}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLeg
      position: {x: .0205504671, y: -.409130007, z: -.000718647963}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightFoot
      position: {x: .00515299942, y: -.423155904, z: -.0276488513}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightToes
      position: {x: .00748699997, y: -.0731673017, z: .145427495}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LToeBase_End3
      position: {x: -.0126400003, y: -.0131357787, z: .0358929969}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Spine
      position: {x: -3.55271347e-16, y: .0922631845, z: .0157713313}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Chest
      position: {x: -0, y: .162540287, z: -.00165605545}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftShoulder
      position: {x: -.0382859968, y: .221622497, z: -.017063085}
      rotation: {x: -.0169460457, y: -.0529774576, z: .15200372, w: .986813664}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftArm
      position: {x: -.100502051, y: 8.52651264e-16, z: -1.91846525e-15}
      rotation: {x: .303798467, y: .015646534, z: -.148740351, w: .940924048}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftForeArm
      position: {x: -.254049301, y: -5.6772363e-14, z: -7.74633667e-13}
      rotation: {x: .0051808483, y: .0260420516, z: -.0195699595, w: .999455869}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHand
      position: {x: -.24638927, y: -1.34519945e-12, z: -1.48500223e-11}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex1
      position: {x: -.0751257986, y: -.00784140453, z: .0326526426}
      rotation: {x: -.0308351107, y: .0179683305, z: .0871939808, w: .995551884}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex2
      position: {x: -.03979728, y: 4.98084046e-05, z: .00118575036}
      rotation: {x: -.0689609796, y: .0147136748, z: .0279580243, w: .997119009}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex3
      position: {x: -.0279684775, y: -6.28308783e-09, z: -5.17217202e-08}
      rotation: {x: 4.9360068e-08, y: .0757969022, z: .0863323957, w: .993378937}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex13
      position: {x: -.0186619665, y: .00437385263, z: -.00384002505}
      rotation: {x: -.00472124433, y: -.101354174, z: -.0462910533, w: .993761659}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle1
      position: {x: -.0760238245, y: -.00188513438, z: .0101412293}
      rotation: {x: -.0157171767, y: .053721305, z: .0864773318, w: .994680226}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle2
      position: {x: -.0442804359, y: 4.79887376e-06, z: -.000425400125}
      rotation: {x: -.0151028167, y: -.00648156414, z: .0269547533, w: .999501586}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle3
      position: {x: -.0339648277, y: -1.2198452e-08, z: 3.7516088e-09}
      rotation: {x: -1.01979772e-07, y: .00856075436, z: .0601487383, w: .998152792}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle13
      position: {x: -.0196715724, y: .00392557262, z: -.000558814383}
      rotation: {x: -.000701564946, y: -.0125020025, z: -.0560236648, w: .998350918}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky1
      position: {x: -.0656599477, y: -.00782510638, z: -.0322512463}
      rotation: {x: -.0464271381, y: .0757413954, z: .0861605853, w: .992312551}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky2
      position: {x: -.0308054481, y: -3.0874573e-05, z: -.0014480775}
      rotation: {x: .0494193174, y: -.0217061825, z: .0329463966, w: .997998536}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky3
      position: {x: -.0230640266, y: -6.40258258e-06, z: 1.8330093e-08}
      rotation: {x: 1.67766084e-05, y: -.0589505993, z: .0381766856, w: .997530639}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky13
      position: {x: -.0169719923, y: .00202882662, z: .00314032286}
      rotation: {x: .000580511638, y: .0944183916, z: -.00612070598, w: .995513618}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing1
      position: {x: -.0703021064, y: -.00374530931, z: -.0114117917}
      rotation: {x: -.0137468018, y: .0746479779, z: .0850574374, w: .993480802}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing2
      position: {x: -.0431354567, y: -2.08823076e-05, z: -.00223517837}
      rotation: {x: .0193584226, y: -.0256355982, z: .0290550962, w: .999061465}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing3
      position: {x: -.0308355652, y: 7.67334946e-11, z: -1.64974168e-08}
      rotation: {x: -1.97207484e-07, y: -.0178757701, z: .0421713889, w: .998950541}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing13
      position: {x: -.0205416381, y: .00325422082, z: .00137918338}
      rotation: {x: .00240248861, y: .0378382765, z: -.063320443, w: .997272789}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb1
      position: {x: -.0142312413, y: -.0123778246, z: .0255316682}
      rotation: {x: -.158041775, y: -.0722015724, z: -.152828872, w: .972858191}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb2
      position: {x: -.0163739994, y: -.00528999977, z: .0234914087}
      rotation: {x: -.0260618888, y: .0966887325, z: .00360755436, w: .994966924}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb3
      position: {x: -.0254599992, y: -.00763999997, z: .0208330005}
      rotation: {x: .00545194838, y: .000442996417, z: .00682530133, w: .999961793}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb13
      position: {x: -.031868957, y: -.0052999449, z: .0258005001}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Neck
      position: {x: 4.26325632e-16, y: .259009302, z: -.0324132554}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Head
      position: {x: 1.27897687e-15, y: .0830703825, z: .0113267815}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: HeadTop_End
      position: {x: -5.17045827e-18, y: .188178778, z: .0121086892}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Jaw
      position: {x: 1.73472344e-20, y: .0111267585, z: .0103275431}
      rotation: {x: .219240054, y: -0, z: -0, w: .975670993}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: JawEND
      position: {x: -1.73472344e-20, y: -.0482887588, z: .071851708}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipCorner
      position: {x: -.032843262, y: -.01657876, z: .0661217645}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipLower
      position: {x: -.0142508168, y: -.0216887593, z: .0822406337}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipCorner
      position: {x: .0328399986, y: -.01657876, z: .0661187842}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipLower
      position: {x: .0142508168, y: -.0216887593, z: .0822387859}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: TongueBack
      position: {x: -1.73472344e-20, y: -.022869369, z: .0100954091}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: TongueTip
      position: {x: -1.73472344e-20, y: -.0232788119, z: .0383227095}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftCheek
      position: {x: -.0542440265, y: .0337019488, z: .0594304018}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEye
      position: {x: -.0208482333, y: .0825027004, z: .0554274321}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEyelidLower
      position: {x: -.0356189571, y: .0650736615, z: .076234743}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEyelidUpper
      position: {x: -.0344068967, y: .10060814, z: .0802053064}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftInnerBrow
      position: {x: -.0120626912, y: .118765265, z: .0934668258}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftIOuterBrow
      position: {x: -.0550398715, y: .114825293, z: .061777398}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipUpper
      position: {x: -.0145013221, y: -.00511181122, z: .094618842}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftNostril
      position: {x: -.0178999994, y: .0263128281, z: .0908674002}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightCheek
      position: {x: .0542399958, y: .033702828, z: .0594273992}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEye
      position: {x: .020849999, y: .082502827, z: .0554273985}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEyelidLower
      position: {x: .0356200002, y: .065072827, z: .0762374029}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEyelidUpper
      position: {x: .0344099998, y: .100612827, z: .0802073926}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightInnerBrow
      position: {x: .0120626874, y: .118765265, z: .0934668258}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightIOuterBrow
      position: {x: .0550400019, y: .114822827, z: .061777398}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipUpper
      position: {x: .0145013221, y: -.00510717137, z: .094617404}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightNostril
      position: {x: .0178999994, y: .0263089053, z: .0908706188}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightShoulder
      position: {x: .0382860154, y: .221621141, z: -.017063085}
      rotation: {x: .148889691, y: .986841977, z: -.0191342514, w: -.0600721166}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightArm
      position: {x: -.100501455, y: -2.49955224e-06, z: -5.15574072e-08}
      rotation: {x: .12166404, y: .961327732, z: -.242588788, w: .0468774885}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightForeArm
      position: {x: .253428251, y: .00601135287, z: -.0167045239}
      rotation: {x: .0546663813, y: .0180182401, z: -.0126463044, w: .998261988}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHand
      position: {x: .245373696, y: .0216417722, z: .00555046508}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex1
      position: {x: .0747694969, y: -.00124305359, z: .0343444981}
      rotation: {x: -.0269991793, y: .134705037, z: -.0601718239, w: .988688529}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex2
      position: {x: .0370584019, y: .00072612107, z: .0145388944}
      rotation: {x: -.0803585127, y: .0230226964, z: .0437488221, w: .995539308}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex3
      position: {x: .0252250377, y: -.00496646529, z: .0110121462}
      rotation: {x: .0205331407, y: -.0777122155, z: -.0820826665, w: .993378937}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex17
      position: {x: .019119978, y: .000846308249, z: .00398164755}
      rotation: {x: -.00472124433, y: -.101354174, z: -.0462910533, w: .993761659}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle1
      position: {x: .0756476447, y: .00479140272, z: .0118531818}
      rotation: {x: -.0139232948, y: .00904202927, z: -.0464321077, w: .998783469}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle2
      position: {x: .0438090637, y: .000194188149, z: .00645493623}
      rotation: {x: -.0214098375, y: -.044567503, z: .0864230916, w: .99503088}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle3
      position: {x: .0330724716, y: -.00754753686, z: .00168984616}
      rotation: {x: .00108972914, y: -.00868750364, z: -.060128659, w: .998152256}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle17
      position: {x: .0200548954, y: -.000547108881, z: .000442590448}
      rotation: {x: -.000701564946, y: -.0125020025, z: -.0560236648, w: .998350918}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky1
      position: {x: .0668033436, y: -.00199410878, z: -.0307561457}
      rotation: {x: -.0533694737, y: -.255000681, z: -.0125761544, w: .96538502}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky2
      position: {x: .0285308417, y: -.001397143, z: -.0116237961}
      rotation: {x: .0333825685, y: .00105689454, z: -.0586909167, w: .997717321}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky3
      position: {x: .0214268602, y: -.000553508929, z: -.00851660781}
      rotation: {x: -.0126826987, y: .0591077842, z: -.0357496776, w: .997530639}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky17
      position: {x: .016975116, y: .00161137758, z: -.00335797085}
      rotation: {x: .000580511638, y: .0944183916, z: -.00612070598, w: .995513618}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing1
      position: {x: .0705984756, y: .00245709647, z: -.00982145779}
      rotation: {x: -.0145361852, y: -.117994301, z: -.0257451385, w: .992574036}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing2
      position: {x: .0428871848, y: -.00137538207, z: -.00494585792}
      rotation: {x: .0220804513, y: -.0216987785, z: .0796155706, w: .996344805}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing3
      position: {x: .0295006037, y: -.00769293541, z: -.00462225592}
      rotation: {x: -.00186288042, y: .0181126203, z: -.0420350544, w: .998950243}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing17
      position: {x: .0206709336, y: -.00200043293, z: -.00177803368}
      rotation: {x: .00240248861, y: .0378382765, z: -.063320443, w: .997272789}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb1
      position: {x: .0146849155, y: -.0111049423, z: .0258580949}
      rotation: {x: -.163419098, y: .0403524339, z: .173200503, w: .970395565}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb2
      position: {x: .0163739994, y: -.00528999977, z: .0234913602}
      rotation: {x: -.0260617808, y: -.0966900066, z: -.00360842934, w: .994966745}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb3
      position: {x: .0254599992, y: -.00763999997, z: .0208330005}
      rotation: {x: .00545424689, y: -.000443457626, z: -.00682825595, w: .999961734}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb17
      position: {x: .0318690389, y: -.00529994583, z: .0258005001}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    armTwist: .5
    foreArmTwist: .5
    upperLegTwist: .5
    legTwist: .5
    armStretch: .0500000007
    legStretch: .0500000007
    feetSpacing: 0
    rootMotionBoneName: 
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 3
  additionalBone: 0
  userData: 
  assetBundleName: 
