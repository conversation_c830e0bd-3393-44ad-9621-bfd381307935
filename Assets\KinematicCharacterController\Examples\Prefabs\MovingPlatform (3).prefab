%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3689664241722883091
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1938043655233188, guid: a92272005d69aed45a7a5b40615946d0,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3689664241722883088}
  - component: {fileID: 3689664241722883100}
  - component: {fileID: 3689664241722883102}
  - component: {fileID: 3689664241722883089}
  - component: {fileID: 3689664241722883098}
  m_Layer: 0
  m_Name: MovingPlatform (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3689664241722883088
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4828515708709470, guid: a92272005d69aed45a7a5b40615946d0,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3689664241722883091}
  m_LocalRotation: {x: -0, y: -0.70710677, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -36.9, y: 5, z: -15.899998}
  m_LocalScale: {x: 3, y: 0.1, z: 3}
  m_Children:
  - {fileID: 3689664242134599910}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &3689664241722883100
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 65168089609025406, guid: a92272005d69aed45a7a5b40615946d0,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3689664241722883091}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &3689664241722883102
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 54627092290138348, guid: a92272005d69aed45a7a5b40615946d0,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3689664241722883091}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &3689664241722883089
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114010447468670020, guid: a92272005d69aed45a7a5b40615946d0,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3689664241722883091}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 075ccbe9837b0744985e09ba3f015b9b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Rigidbody: {fileID: 3689664241722883102}
  MoveWithPhysics: 1
--- !u!114 &3689664241722883098
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3689664241722883091}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b7edc6957dab9ac4797c1c0912ce3839, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Mover: {fileID: 3689664241722883089}
  TranslationAxis: {x: 0, y: 1, z: 0}
  TranslationPeriod: 4.9
  TranslationSpeed: 2
  RotationAxis: {x: 0, y: 1, z: 0}
  RotSpeed: 0
  OscillationAxis: {x: 0, y: 0, z: 0}
  OscillationPeriod: 10
  OscillationSpeed: 10
--- !u!1 &3689664242134599929
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3689664242134599910}
  - component: {fileID: 3689664242134599908}
  - component: {fileID: 3689664242134599911}
  m_Layer: 0
  m_Name: Root (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3689664242134599910
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3689664242134599929}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 3689664241722883088}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &3689664242134599908
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3689664242134599929}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 60c42fb470eea2b4198867ba20e2b905, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &3689664242134599911
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3689664242134599929}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
