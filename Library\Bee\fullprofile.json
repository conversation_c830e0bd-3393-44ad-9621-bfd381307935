{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 35676, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 35676, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 35676, "tid": 478, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 35676, "tid": 478, "ts": 1756605241074413, "dur": 563, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 35676, "tid": 478, "ts": 1756605241077748, "dur": 600, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 35676, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238696420, "dur": 17886, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238714307, "dur": 2353314, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238714311, "dur": 23, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238714337, "dur": 19191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238733533, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238733566, "dur": 4, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238733571, "dur": 2635, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736211, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736258, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736260, "dur": 39, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736300, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736302, "dur": 34, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736339, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736341, "dur": 40, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736384, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736386, "dur": 40, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736428, "dur": 1, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736430, "dur": 37, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736469, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736472, "dur": 31, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736504, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736506, "dur": 36, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736545, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736589, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736591, "dur": 42, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736635, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736637, "dur": 34, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736674, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736675, "dur": 36, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736714, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736716, "dur": 36, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736754, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736756, "dur": 33, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736791, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736793, "dur": 28, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736824, "dur": 27, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736854, "dur": 24, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736881, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736906, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736937, "dur": 28, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238736968, "dur": 35, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737005, "dur": 30, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737038, "dur": 26, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737067, "dur": 28, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737097, "dur": 23, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737123, "dur": 27, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737153, "dur": 23, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737179, "dur": 22, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737203, "dur": 24, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737228, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737230, "dur": 24, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737256, "dur": 22, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737281, "dur": 22, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737306, "dur": 31, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737339, "dur": 25, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737367, "dur": 25, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737394, "dur": 27, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737424, "dur": 23, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737450, "dur": 25, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737477, "dur": 24, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737504, "dur": 27, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737533, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737567, "dur": 25, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737596, "dur": 25, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737623, "dur": 23, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737649, "dur": 23, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737675, "dur": 24, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737701, "dur": 28, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737731, "dur": 24, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737757, "dur": 29, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737789, "dur": 25, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737816, "dur": 23, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737842, "dur": 30, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737875, "dur": 25, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737902, "dur": 31, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737935, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737937, "dur": 34, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737972, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238737974, "dur": 30, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738006, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738008, "dur": 31, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738043, "dur": 25, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738071, "dur": 31, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738105, "dur": 20, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738127, "dur": 24, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738154, "dur": 22, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738178, "dur": 31, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738212, "dur": 24, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738239, "dur": 24, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738265, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738266, "dur": 28, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738297, "dur": 28, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738327, "dur": 23, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738352, "dur": 30, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738385, "dur": 27, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738415, "dur": 27, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738446, "dur": 33, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738482, "dur": 23, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738508, "dur": 33, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738543, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738570, "dur": 26, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738599, "dur": 25, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738626, "dur": 25, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738654, "dur": 24, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738680, "dur": 24, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738707, "dur": 29, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738739, "dur": 23, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738765, "dur": 22, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738789, "dur": 59, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738851, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738853, "dur": 27, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738882, "dur": 35, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738920, "dur": 25, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738948, "dur": 30, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738980, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238738981, "dur": 24, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739007, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739031, "dur": 26, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739059, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739084, "dur": 32, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739119, "dur": 25, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739146, "dur": 29, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739178, "dur": 23, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739203, "dur": 26, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739232, "dur": 26, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739261, "dur": 23, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739287, "dur": 26, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739315, "dur": 23, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739341, "dur": 24, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739367, "dur": 32, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739402, "dur": 28, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739432, "dur": 29, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739464, "dur": 24, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739491, "dur": 26, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739520, "dur": 23, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739545, "dur": 23, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739570, "dur": 25, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739598, "dur": 33, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739634, "dur": 25, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739661, "dur": 25, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739688, "dur": 40, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739732, "dur": 22, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739757, "dur": 24, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739783, "dur": 28, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739814, "dur": 23, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739840, "dur": 32, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739875, "dur": 24, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739901, "dur": 31, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739935, "dur": 25, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739962, "dur": 23, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238739987, "dur": 118, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740108, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740134, "dur": 23, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740160, "dur": 28, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740191, "dur": 23, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740217, "dur": 23, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740242, "dur": 23, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740268, "dur": 59, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740329, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740331, "dur": 29, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740362, "dur": 23, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740389, "dur": 23, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740414, "dur": 25, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740443, "dur": 25, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740470, "dur": 26, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740499, "dur": 22, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740523, "dur": 22, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740548, "dur": 23, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740573, "dur": 33, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740608, "dur": 31, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740642, "dur": 26, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740671, "dur": 28, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740700, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740702, "dur": 23, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740728, "dur": 25, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740755, "dur": 22, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740780, "dur": 31, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740814, "dur": 24, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740841, "dur": 25, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740867, "dur": 24, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740894, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740918, "dur": 23, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740944, "dur": 24, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740970, "dur": 23, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238740997, "dur": 25, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741024, "dur": 22, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741049, "dur": 21, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741073, "dur": 22, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741098, "dur": 28, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741128, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741164, "dur": 27, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741193, "dur": 29, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741225, "dur": 27, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741254, "dur": 27, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741284, "dur": 46, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741332, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741333, "dur": 32, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741367, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741368, "dur": 23, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741395, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741422, "dur": 26, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741450, "dur": 25, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741478, "dur": 26, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741507, "dur": 34, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741544, "dur": 29, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741575, "dur": 26, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741603, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741604, "dur": 34, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741642, "dur": 32, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741676, "dur": 29, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741708, "dur": 29, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741740, "dur": 25, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741767, "dur": 25, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741795, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741822, "dur": 27, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741852, "dur": 32, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741886, "dur": 25, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741914, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741937, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238741963, "dur": 47, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742012, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742014, "dur": 36, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742053, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742055, "dur": 32, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742090, "dur": 30, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742123, "dur": 24, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742149, "dur": 34, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742186, "dur": 28, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742216, "dur": 26, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742245, "dur": 28, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742277, "dur": 31, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742310, "dur": 24, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742337, "dur": 30, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742371, "dur": 30, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742404, "dur": 34, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742439, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742441, "dur": 27, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742471, "dur": 28, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742502, "dur": 27, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742531, "dur": 29, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742563, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742565, "dur": 32, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742600, "dur": 25, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742627, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742629, "dur": 51, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742683, "dur": 24, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742710, "dur": 29, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742741, "dur": 24, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742768, "dur": 26, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742796, "dur": 25, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742824, "dur": 24, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742851, "dur": 27, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742881, "dur": 27, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742910, "dur": 25, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742937, "dur": 23, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742962, "dur": 31, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238742996, "dur": 41, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743040, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743042, "dur": 28, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743071, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743073, "dur": 31, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743106, "dur": 25, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743134, "dur": 26, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743162, "dur": 29, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743194, "dur": 39, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743234, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743236, "dur": 30, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743268, "dur": 23, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743294, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743320, "dur": 23, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743344, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743370, "dur": 24, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743396, "dur": 25, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743424, "dur": 22, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743448, "dur": 34, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743487, "dur": 27, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743516, "dur": 124, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743645, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743686, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743689, "dur": 36, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743728, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743730, "dur": 41, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743775, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743778, "dur": 49, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743830, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743835, "dur": 47, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743884, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743887, "dur": 43, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743933, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743936, "dur": 37, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743976, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238743978, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744027, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744029, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744073, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744076, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744110, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744112, "dur": 39, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744155, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744157, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744201, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744203, "dur": 37, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744243, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744290, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744293, "dur": 39, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744333, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744336, "dur": 37, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744375, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744378, "dur": 40, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744421, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744424, "dur": 37, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744464, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744466, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744511, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744514, "dur": 47, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744565, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744568, "dur": 55, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744626, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744629, "dur": 46, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744677, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744680, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744717, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744719, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744758, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744760, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744807, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238744853, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238745016, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238745050, "dur": 1629, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238746684, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238746723, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238746726, "dur": 912, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238747644, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238747681, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238747683, "dur": 144, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238747833, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238747866, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238747976, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748016, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748035, "dur": 33, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748071, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748073, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748107, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748143, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748172, "dur": 616, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748793, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748828, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748863, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748938, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238748972, "dur": 301, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238749278, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238749318, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238749350, "dur": 186, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238749540, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238749575, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238749578, "dur": 30, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238749610, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238749612, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238749644, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238749673, "dur": 306, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238749982, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750026, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750028, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750064, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750066, "dur": 38, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750107, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750109, "dur": 36, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750147, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750149, "dur": 37, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750189, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750191, "dur": 35, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750228, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750229, "dur": 36, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750268, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750270, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750311, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750313, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750358, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750388, "dur": 187, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750580, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750623, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750625, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750667, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750670, "dur": 62, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750737, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750772, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750774, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750812, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750844, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750900, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750942, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238750973, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751037, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751072, "dur": 92, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751167, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751201, "dur": 187, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751393, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751431, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751466, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751516, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751542, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751642, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751680, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751713, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751751, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751790, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751820, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238751822, "dur": 356, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752183, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752220, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752250, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752275, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752323, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752348, "dur": 217, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752569, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752606, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752643, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752677, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752735, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752763, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752922, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752952, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238752984, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238753010, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238753054, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238753079, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238753105, "dur": 445, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238753552, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238753579, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605238753581, "dur": 1111346, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239864936, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239864940, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239865004, "dur": 841, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239865848, "dur": 670, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239866523, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239866560, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239866562, "dur": 596, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239867164, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239867201, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239867204, "dur": 1543, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239868752, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239868792, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605239868803, "dur": 460955, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605240329766, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605240329770, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605240329823, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605240329853, "dur": 725277, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241055138, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241055142, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241055189, "dur": 249, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241055440, "dur": 1576, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241057022, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241057073, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241057076, "dur": 1540, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241058622, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241058684, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241058711, "dur": 263, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241058977, "dur": 11, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241058990, "dur": 409, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241059404, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241059455, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 35676, "tid": 21474836480, "ts": 1756605241059458, "dur": 8160, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 35676, "tid": 478, "ts": 1756605241078351, "dur": 553, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 35676, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 35676, "tid": 17179869184, "ts": 1756605238696393, "dur": 3, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 35676, "tid": 17179869184, "ts": 1756605238696396, "dur": 17902, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 35676, "tid": 17179869184, "ts": 1756605238714298, "dur": 31, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 35676, "tid": 478, "ts": 1756605241078907, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 35676, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35676, "tid": 1, "ts": 1756605237996029, "dur": 4172, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 35676, "tid": 1, "ts": 1756605238000203, "dur": 18686, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 35676, "tid": 1, "ts": 1756605238018893, "dur": 18648, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 35676, "tid": 478, "ts": 1756605241078922, "dur": 4, "ph": "X", "name": "", "args": {}}, {"pid": 35676, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605237994437, "dur": 13192, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238007630, "dur": 431647, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238008423, "dur": 4791, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238013219, "dur": 1045, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238014267, "dur": 149, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238014419, "dur": 369, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238014790, "dur": 86, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238014880, "dur": 5, "ph": "X", "name": "ProcessMessages 7623", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238014886, "dur": 50, "ph": "X", "name": "ReadAsync 7623", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238014940, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238014943, "dur": 48, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238014994, "dur": 1, "ph": "X", "name": "ProcessMessages 987", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238014996, "dur": 36, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015035, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015036, "dur": 32, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015070, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015072, "dur": 32, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015106, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015108, "dur": 32, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015144, "dur": 27, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015174, "dur": 32, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015209, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015211, "dur": 39, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015254, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015256, "dur": 26, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015285, "dur": 25, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015313, "dur": 28, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015344, "dur": 31, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015377, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015379, "dur": 29, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015410, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015411, "dur": 31, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015444, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015446, "dur": 30, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015479, "dur": 28, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015511, "dur": 29, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015542, "dur": 31, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015575, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015577, "dur": 19, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015598, "dur": 30, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015629, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015631, "dur": 30, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015664, "dur": 37, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015704, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015738, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015740, "dur": 30, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015772, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015774, "dur": 32, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015807, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015809, "dur": 28, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015840, "dur": 30, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015872, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015873, "dur": 29, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015905, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015906, "dur": 26, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015936, "dur": 28, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015968, "dur": 28, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238015998, "dur": 31, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016032, "dur": 30, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016064, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016066, "dur": 25, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016093, "dur": 23, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016118, "dur": 28, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016149, "dur": 24, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016176, "dur": 25, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016204, "dur": 24, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016231, "dur": 23, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016257, "dur": 30, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016289, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016291, "dur": 35, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016329, "dur": 1, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016332, "dur": 46, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016380, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016382, "dur": 34, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016418, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016420, "dur": 33, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016457, "dur": 38, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016498, "dur": 28, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016528, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016529, "dur": 22, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016553, "dur": 34, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016591, "dur": 27, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016620, "dur": 32, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016655, "dur": 25, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016682, "dur": 26, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016711, "dur": 25, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016738, "dur": 33, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016774, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016775, "dur": 37, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016815, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016819, "dur": 36, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016857, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016859, "dur": 42, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016902, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016904, "dur": 34, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016940, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016942, "dur": 25, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238016970, "dur": 28, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017000, "dur": 25, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017028, "dur": 23, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017053, "dur": 33, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017089, "dur": 24, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017116, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017139, "dur": 155, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017298, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017336, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017338, "dur": 25, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017365, "dur": 28, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017395, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017397, "dur": 23, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017422, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017449, "dur": 24, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017475, "dur": 31, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017509, "dur": 25, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017538, "dur": 25, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017566, "dur": 24, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017592, "dur": 23, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017618, "dur": 26, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017647, "dur": 43, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017693, "dur": 2, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017697, "dur": 65, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017765, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017767, "dur": 40, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017810, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017812, "dur": 42, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017857, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017859, "dur": 49, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017910, "dur": 1, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017912, "dur": 28, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017941, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017943, "dur": 24, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017969, "dur": 22, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017993, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238017995, "dur": 31, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018029, "dur": 42, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018073, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018075, "dur": 26, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018103, "dur": 32, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018137, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018139, "dur": 41, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018183, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018186, "dur": 31, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018218, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018220, "dur": 29, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018252, "dur": 31, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018286, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018287, "dur": 29, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018318, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018320, "dur": 24, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018346, "dur": 23, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018372, "dur": 29, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018403, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018449, "dur": 28, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018480, "dur": 24, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018506, "dur": 22, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018531, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018558, "dur": 28, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018589, "dur": 33, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018624, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018625, "dur": 27, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018654, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018655, "dur": 42, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018701, "dur": 32, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018735, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018736, "dur": 24, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018762, "dur": 28, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018793, "dur": 21, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018818, "dur": 33, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018853, "dur": 23, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018878, "dur": 27, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018908, "dur": 24, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018934, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018958, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238018984, "dur": 34, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019020, "dur": 23, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019046, "dur": 27, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019076, "dur": 24, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019103, "dur": 27, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019132, "dur": 25, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019160, "dur": 23, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019185, "dur": 29, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019215, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019217, "dur": 22, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019242, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019269, "dur": 23, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019295, "dur": 23, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019321, "dur": 29, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019352, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019377, "dur": 27, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019406, "dur": 24, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019433, "dur": 23, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019458, "dur": 24, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019484, "dur": 24, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019511, "dur": 22, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019535, "dur": 30, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019568, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019570, "dur": 29, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019601, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019602, "dur": 31, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019636, "dur": 34, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019672, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019673, "dur": 30, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019704, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019706, "dur": 44, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019752, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019754, "dur": 30, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019786, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019787, "dur": 23, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019813, "dur": 25, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019840, "dur": 27, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019870, "dur": 21, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019893, "dur": 22, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019919, "dur": 26, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019947, "dur": 24, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019974, "dur": 23, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238019999, "dur": 23, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020025, "dur": 25, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020053, "dur": 20, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020074, "dur": 26, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020103, "dur": 23, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020129, "dur": 23, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020154, "dur": 24, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020181, "dur": 23, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020206, "dur": 22, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020230, "dur": 32, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020264, "dur": 24, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020291, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020317, "dur": 24, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020344, "dur": 28, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020376, "dur": 23, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020402, "dur": 24, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020429, "dur": 23, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020454, "dur": 24, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020481, "dur": 23, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020507, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020530, "dur": 32, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020565, "dur": 24, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020593, "dur": 29, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020624, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020625, "dur": 34, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020663, "dur": 26, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020692, "dur": 35, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020730, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020732, "dur": 123, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020860, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238020898, "dur": 287, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021188, "dur": 69, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021260, "dur": 8, "ph": "X", "name": "ProcessMessages 1228", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021270, "dur": 54, "ph": "X", "name": "ReadAsync 1228", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021328, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021331, "dur": 40, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021374, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021377, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021412, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021414, "dur": 43, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021460, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021462, "dur": 44, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021508, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021511, "dur": 22, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021535, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021537, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021572, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021574, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021622, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021624, "dur": 40, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021666, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021671, "dur": 38, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021710, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021712, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021756, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021758, "dur": 42, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021803, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021806, "dur": 48, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021856, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021859, "dur": 35, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021896, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021897, "dur": 32, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021933, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021934, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021973, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238021975, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238022013, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238022015, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238022050, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238022052, "dur": 2601, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238024658, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238024684, "dur": 59, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238024748, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238024786, "dur": 414, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238025204, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238025240, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238025242, "dur": 110, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238025356, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238025389, "dur": 343, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238025736, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238025775, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238025803, "dur": 126, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238025933, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238025962, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026088, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026124, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026161, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026163, "dur": 126, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026292, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026337, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026340, "dur": 36, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026378, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026380, "dur": 422, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026807, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026847, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026850, "dur": 43, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026895, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026897, "dur": 27, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026927, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026961, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238026963, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027006, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027008, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027040, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027041, "dur": 27, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027072, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027094, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027162, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027186, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027261, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027298, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027321, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027359, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027398, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027432, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027434, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027458, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027493, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027531, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027533, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027570, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027610, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027645, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027728, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027760, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027822, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027860, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027861, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238027891, "dur": 395, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238028289, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238028328, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238028365, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238028367, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238028422, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238028449, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238028473, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238028654, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238028692, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238028722, "dur": 331, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238029056, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238029085, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238029114, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238029142, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238029195, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238029221, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238029244, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238029430, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238029464, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238031813, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238031816, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238031855, "dur": 346, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238032203, "dur": 397633, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238429842, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238429845, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238429893, "dur": 6187, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238436082, "dur": 45, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238436130, "dur": 191, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 35676, "tid": 12884901888, "ts": 1756605238436323, "dur": 2934, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 35676, "tid": 478, "ts": 1756605241078928, "dur": 419, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 35676, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 35676, "tid": 8589934592, "ts": 1756605237992261, "dur": 45327, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 35676, "tid": 8589934592, "ts": 1756605238037590, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 35676, "tid": 8589934592, "ts": 1756605238037593, "dur": 904, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 35676, "tid": 478, "ts": 1756605241079350, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 35676, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 35676, "tid": 4294967296, "ts": 1756605237978454, "dur": 461554, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 35676, "tid": 4294967296, "ts": 1756605237981876, "dur": 7014, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 35676, "tid": 4294967296, "ts": 1756605238440095, "dur": 251361, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 35676, "tid": 4294967296, "ts": 1756605238691625, "dur": 2376031, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 35676, "tid": 4294967296, "ts": 1756605238691708, "dur": 4657, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 35676, "tid": 4294967296, "ts": 1756605241067665, "dur": 4672, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 35676, "tid": 4294967296, "ts": 1756605241070799, "dur": 42, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 35676, "tid": 4294967296, "ts": 1756605241072341, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 35676, "tid": 478, "ts": 1756605241079355, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756605238714280, "dur": 21045, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238735331, "dur": 102, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238735480, "dur": 619, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238736137, "dur": 7304, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238743448, "dur": 2315408, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605241058883, "dur": 51, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605241058935, "dur": 64, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605241059044, "dur": 52, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605241059253, "dur": 68, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605241059342, "dur": 1896, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756605238736138, "dur": 7318, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238743461, "dur": 876, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238744364, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1756605238744642, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238744883, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238745472, "dur": 932, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\NesterState.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756605238745396, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238746537, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238746731, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238747259, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238747441, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238747680, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238747942, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238748261, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238748788, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238749262, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238749332, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756605238749494, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756605238750119, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756605238750243, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756605238750579, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238750730, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238750883, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238751378, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238751646, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238751754, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238752195, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238752615, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238752914, "dur": 1113121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605239866038, "dur": 407, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1756605239866037, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1756605239866475, "dur": 2227, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1756605239868705, "dur": 1189984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238736233, "dur": 7258, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238743498, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_2AB2B6500CC2976C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756605238743830, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238743922, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238744152, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756605238744360, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756605238744613, "dur": 353, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756605238744966, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238745381, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238745620, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238745851, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238746078, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238746282, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238746593, "dur": 678, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\OperatorHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756605238746513, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238747380, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238747588, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238747770, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238748058, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238748332, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238748796, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238749282, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238749350, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238750134, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238750247, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238750584, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238750729, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238750882, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238751019, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238751399, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238751636, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238751742, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238752184, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238752623, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238752917, "dur": 1113116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605239866036, "dur": 410, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756605239866035, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756605239866480, "dur": 463201, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756605240329684, "dur": 729008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238736163, "dur": 7303, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238743481, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_AC6036D9AAB7806A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756605238743648, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238743827, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_41D0FA408EE9AC25.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756605238743922, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238744223, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756605238744607, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238744900, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238745308, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238745573, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238745795, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238746042, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238746259, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238746587, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\BinaryOperatorHandler.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756605238746521, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238747298, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238747486, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238747744, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238747962, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238748214, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238748772, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238749263, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238749330, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756605238749515, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756605238750168, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238750218, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238750589, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238750731, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238750905, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238751424, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238751644, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238751746, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238752183, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238752575, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238752632, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238752919, "dur": 1113136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605239866056, "dur": 1192674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238736212, "dur": 7269, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238743488, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_541FF46A9EE101D0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238743616, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756605238743615, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_FDFB36A4E7308FA7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238743698, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_FDFB36A4E7308FA7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238743928, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756605238743927, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_DA2D9EF91183F51C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238744069, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1756605238744144, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1756605238744230, "dur": 366, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1756605238744629, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238744843, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238745306, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238745600, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238745798, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238745997, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238746205, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238746623, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238747126, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238747333, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238747517, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238747795, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238748020, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238748092, "dur": 65, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238748219, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238748773, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238749261, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238749334, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238749519, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756605238750096, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238750232, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756605238750908, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238751417, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238751638, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238751734, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238752172, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238752598, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238752907, "dur": 1113130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605239866038, "dur": 1190511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605241056552, "dur": 386, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756605241056551, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756605241056954, "dur": 1697, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756605238736257, "dur": 7246, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238743516, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756605238743509, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_2F75BA7C0CD3BB01.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238743584, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_2F75BA7C0CD3BB01.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238743818, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_B29023F536B846E5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238744072, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238744280, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756605238744551, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756605238744673, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238744928, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238745259, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238745451, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238745751, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238745956, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238746162, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238746375, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238747127, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238747394, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238747613, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238747838, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238748345, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238748799, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238749285, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238749347, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238749493, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238749546, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756605238750134, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238750234, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238750602, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238750743, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238750889, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238751393, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238751646, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238751746, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238752187, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238752646, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238752924, "dur": 1113144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605239866069, "dur": 1192633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238736297, "dur": 7218, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238743742, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_F133778AA9D7537D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238743918, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238744058, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1756605238744152, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1756605238744314, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756605238744559, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1756605238744692, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238744935, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238745432, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238745668, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238745864, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238746076, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238746294, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238746528, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238746721, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238747234, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238747427, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238747628, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238747857, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238748235, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238748781, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238749339, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238749509, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756605238749974, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238750090, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 6, "ts": 1756605238750146, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238750222, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238750586, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238750737, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756605238751650, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238751751, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756605238752209, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238752282, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756605238752621, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238752689, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756605238752943, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238753203, "dur": 254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756605238753014, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756605238753505, "dur": 1110973, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756605239866303, "dur": 305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1756605239866036, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756605239867110, "dur": 1187973, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756605241056549, "dur": 409, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1756605241056547, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1756605241056969, "dur": 1605, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1756605241058577, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238736323, "dur": 7201, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238743768, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1B32608D121D92D4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238743962, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756605238743961, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_76CD42B8F77F64CB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238744112, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238744744, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756605238744942, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756605238746797, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestEnumeratorWrapper.cs"}}, {"pid": 12345, "tid": 7, "ts": 1756605238744215, "dur": 3316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756605238747607, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238747779, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756605238748787, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238748891, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756605238749344, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238749497, "dur": 1034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756605238750586, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238750695, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756605238751397, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238751472, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756605238751767, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238752183, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238752639, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238752921, "dur": 1113145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605239866067, "dur": 1192742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238736358, "dur": 7177, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238743659, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1756605238743657, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_DFDA616A813F02A0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756605238743726, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_DFDA616A813F02A0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756605238743827, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_8F3B383E820B44F3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756605238743935, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238744069, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_0F23B24153FF954C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756605238744363, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1756605238744609, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238744826, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238745160, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238745413, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238745638, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238745852, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238746047, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238746274, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238746509, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238746718, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238747224, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238747738, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238748241, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238748784, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238749274, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238749355, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238750027, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238750119, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 8, "ts": 1756605238750228, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238750611, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238750749, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238750889, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238751375, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238751633, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238751735, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238752213, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756605238752294, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756605238752614, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238752947, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756605238753038, "dur": 1113002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605239866041, "dur": 1192685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238736390, "dur": 7157, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238743640, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756605238743639, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_F03DEA8A4A03F5B1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756605238743930, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756605238743929, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_802F649CCD1799B5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756605238744030, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756605238744029, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_0014B637A0C5589A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756605238744146, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756605238744361, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756605238744563, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756605238744808, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238745242, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238745479, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238745689, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238745893, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238746139, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238746347, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238746573, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238747117, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238747375, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238747589, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238747843, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238748227, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238748794, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238749275, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238749349, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756605238749479, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238749604, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756605238750050, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238750139, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238750249, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238750606, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238750758, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238750895, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238751441, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238751652, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238751758, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238752176, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238752571, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238752660, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238752928, "dur": 1113146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605239866074, "dur": 1192635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238736424, "dur": 7136, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238743630, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238743772, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C3A9D733D8F13DA7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756605238743898, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_251F7B5CCFDC4437.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756605238744000, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238744101, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1756605238744545, "dur": 430, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756605238744975, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238745411, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238745647, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238745857, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238746070, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238746328, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238746557, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238746756, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238748089, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238748276, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238748797, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238749283, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238749361, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238750019, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238750139, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238750220, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238750604, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238750767, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238750888, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238751376, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238751631, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238751737, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238752189, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238752607, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238752927, "dur": 1113153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605239866081, "dur": 1192637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238736463, "dur": 7111, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238743662, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1756605238743646, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_A35037EF751D04E0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238743875, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4861E4C2635A17FD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238743950, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\PlaybackEngines\\MetroSupport\\UnityEditor.UWP.Extensions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756605238743949, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_85AF19AB75C0E206.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238744225, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1756605238744368, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1756605238744552, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1756605238744682, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238744908, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238745277, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238745557, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238745772, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238745993, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238746196, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238746464, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238746668, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238747188, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238747389, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238747601, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238747840, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238748084, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238748223, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238748795, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238749277, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238749342, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238749513, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756605238749891, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238749950, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238750031, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238750132, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238750221, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238750597, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238750749, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238750892, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238751400, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238751639, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238751753, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238752192, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238752562, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238752672, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238752911, "dur": 1113142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605239866053, "dur": 1192679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238736514, "dur": 7081, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238743609, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1756605238743601, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_4560EE608FD1CED1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756605238743762, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_AD23277E4971799F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756605238743879, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_5E00F98393042396.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756605238743976, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238744239, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756605238744452, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1756605238744611, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238744813, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238745200, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238745451, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238745684, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238745886, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238746101, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238746348, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238747172, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238747405, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238747855, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238748368, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238748802, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238749267, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238749336, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756605238749492, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756605238749880, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238750129, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756605238750183, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756605238750311, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756605238750764, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238750895, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238751448, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238751651, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238751752, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238752189, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238752572, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238752653, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238752926, "dur": 1113139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605239866065, "dur": 1192659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238736548, "dur": 7058, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238743767, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_1904DB9DCBD3120A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756605238743894, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_5D6B66C50E585513.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756605238743987, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238744050, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1756605238744148, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1756605238744483, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756605238744632, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238744864, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238745248, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238745492, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238745693, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238745886, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238746103, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238746421, "dur": 701, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Canvases\\IGraphContextExtension.cs"}}, {"pid": 12345, "tid": 13, "ts": 1756605238746308, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238747230, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238747597, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238747779, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238748052, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238748285, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238748793, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238749269, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238749338, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756605238749522, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756605238750595, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756605238750687, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756605238751009, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238751409, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238751640, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238751744, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238752179, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238752600, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238752922, "dur": 1113141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605239866064, "dur": 1192673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238736587, "dur": 7038, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238743930, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D6E75BBCA6D3CFE8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756605238744144, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756605238746487, "dur": 1463, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756605238747951, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238748226, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238748790, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238749270, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238749493, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238750154, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238750227, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238750596, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756605238750777, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756605238751139, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238751390, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238751635, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238751750, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238752185, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238752599, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238752907, "dur": 1113132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605239866039, "dur": 1192682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238736613, "dur": 7056, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238743767, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_4AA12695882D873C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756605238743872, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9CF97525CFB8C915.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756605238743932, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756605238743930, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_A2B6EC8E55EE003B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756605238744075, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756605238744322, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756605238744945, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756605238745094, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756605238745214, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756605238745505, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756605238745630, "dur": 402, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756605238746800, "dur": 304, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756605238747105, "dur": 322, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskableGraphic.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756605238747699, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\PositionAsUV1.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756605238744439, "dur": 3475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756605238747914, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238748022, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238748225, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238748780, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238749284, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238749349, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756605238749839, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756605238749953, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756605238749530, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756605238750236, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238750594, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238750730, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238750883, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238751146, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238751380, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238751656, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238751738, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238752171, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238752617, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238752917, "dur": 1113140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605239866057, "dur": 1192633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238736660, "dur": 7019, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238743937, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756605238743936, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DE0AF2B67DDA2EE7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756605238744059, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756605238744277, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756605238744550, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756605238744655, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238744893, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238745301, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238745548, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238745756, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238745986, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238746206, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238746446, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238746653, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238747208, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238747410, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238747618, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238747846, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238748225, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238748792, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238749268, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238749342, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756605238749503, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756605238749935, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238750022, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238750129, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1756605238750228, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238750618, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238750741, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238750897, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238751433, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238751647, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238751755, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238752194, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238752561, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238752666, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238752931, "dur": 1113117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605239866049, "dur": 1192665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605241064883, "dur": 1835, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1756605238512407, "dur": 161767, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756605238513284, "dur": 47292, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756605238648532, "dur": 3661, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756605238652196, "dur": 21971, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756605238653566, "dur": 16928, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756605238679817, "dur": 1042, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756605238679488, "dur": 1536, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756605238006222, "dur": 2861, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238009092, "dur": 283, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238009427, "dur": 724, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238011266, "dur": 3064, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C43CDFEF27905A5E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756605238014714, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C7D5B828D2D2777E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756605238010191, "dur": 10419, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238020617, "dur": 18049, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238038666, "dur": 392607, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238432372, "dur": 57, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238432442, "dur": 1618, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756605238010117, "dur": 10509, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238020658, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756605238020640, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_AC6036D9AAB7806A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756605238021020, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238021123, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_88EDA310933505FB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756605238021247, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756605238021563, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1756605238021770, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238021995, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238022362, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238022560, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238023731, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238024271, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238024466, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238024736, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238024922, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238025248, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238025666, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238026061, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756605238026199, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756605238026782, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756605238026940, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756605238027068, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756605238027770, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756605238028242, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756605238028368, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756605238029010, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756605238029128, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756605238029492, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756605238029563, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756605238029822, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756605238029897, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756605238031055, "dur": 398691, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756605238010242, "dur": 10438, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238020794, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_F278B156303A0AAC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756605238021037, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238021118, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1756605238021206, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756605238021399, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1756605238021797, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238022043, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238022407, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238022656, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238022886, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238023100, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238023790, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238024308, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238024547, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238024759, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238024947, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238025182, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238025669, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238026057, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756605238026232, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756605238026739, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756605238026861, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756605238026973, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756605238027407, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238027520, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238027787, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238028259, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238028608, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238029013, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238029386, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238029496, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756605238029801, "dur": 8871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238010138, "dur": 10501, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238020644, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_541FF46A9EE101D0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756605238020746, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_541FF46A9EE101D0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756605238020827, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756605238020825, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_DFDA616A813F02A0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756605238021029, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756605238021028, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DE0AF2B67DDA2EE7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756605238021264, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1756605238021509, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756605238021659, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756605238021738, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756605238021839, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238022239, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238022438, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238022674, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238022923, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238023128, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238023343, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238023552, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238023758, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238024319, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238024521, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238024715, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238024921, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238025198, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238025684, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238026085, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238026760, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756605238026823, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238026927, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238027196, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238027298, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238027392, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238027498, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238027752, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238028258, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238028604, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238029038, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238029400, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238029503, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756605238029811, "dur": 8879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238010173, "dur": 10474, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238020652, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_2AB2B6500CC2976C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238020738, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_2AB2B6500CC2976C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238020901, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_C31D4F40F9D0C2BA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238021021, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238021143, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238021345, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1756605238021795, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238022270, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238022478, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238022765, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238022984, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238023180, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238023417, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238023619, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238023835, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238024367, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238024601, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238024841, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238025187, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238025685, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238026065, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238026213, "dur": 920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756605238027221, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238027334, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756605238027765, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238028250, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238028591, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238029012, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756605238029099, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756605238029384, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238029494, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756605238029803, "dur": 8874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238010212, "dur": 10445, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238020663, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_2F75BA7C0CD3BB01.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238020738, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_2F75BA7C0CD3BB01.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238020828, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756605238020827, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_E6BEA26732798AFD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238020978, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C2059134513FAD1E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238021066, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238021190, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1756605238021503, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756605238021647, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756605238021736, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1756605238021795, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238022210, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238022850, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238023058, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238023740, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238024226, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238024422, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238024740, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238024936, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238025185, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238025671, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238026060, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238026206, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756605238026738, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238026814, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238026948, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756605238027333, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238027515, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238027782, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238028249, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238028607, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238029023, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238029407, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238029505, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756605238029823, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756605238029916, "dur": 8785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238010238, "dur": 10430, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238020686, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1756605238020676, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_EADBF35E0F2C1306.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238021020, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1756605238021019, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_802F649CCD1799B5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238021125, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_802F649CCD1799B5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238021211, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1756605238021436, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756605238021709, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1756605238021780, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238022217, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238022436, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238022730, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238022941, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238023162, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238023389, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238023585, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238023823, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238024368, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238024643, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238024898, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238025185, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238025687, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238026079, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756605238026233, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756605238026800, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238026915, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238027187, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238027311, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238027400, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238027531, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238027768, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238028242, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238028588, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238028994, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238029364, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238029486, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756605238029799, "dur": 8863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238010273, "dur": 10419, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238020810, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756605238020809, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_146F44193477D9A2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238021030, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\PlaybackEngines\\MetroSupport\\UnityEditor.UWP.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756605238021029, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_85AF19AB75C0E206.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238021214, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756605238021372, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238021911, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756605238022016, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756605238022438, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756605238022658, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756605238023695, "dur": 513, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Navigation.cs"}}, {"pid": 12345, "tid": 7, "ts": 1756605238021455, "dur": 3058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756605238024581, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238024783, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238024897, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238025196, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238025691, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238026062, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238026188, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756605238026546, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238026787, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756605238026916, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238027225, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238027327, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756605238027433, "dur": 727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756605238028255, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238028605, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238029030, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238029414, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238029504, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756605238029810, "dur": 8872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238010312, "dur": 10394, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238021070, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238021144, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756605238021244, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756605238021384, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1756605238021553, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1756605238021743, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756605238021923, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238022430, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238022709, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238023390, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238023601, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238024487, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Reflection\\fsMetaType.cs"}}, {"pid": 12345, "tid": 8, "ts": 1756605238023816, "dur": 1431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238025247, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238025685, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238026098, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238026788, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1756605238026918, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238027188, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238027327, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756605238027641, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238027760, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238028253, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238028596, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238029068, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238029367, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238029505, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756605238029793, "dur": 8871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238010335, "dur": 10381, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238020974, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0C251660B714137C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756605238021057, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238021135, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1756605238021304, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756605238021390, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1756605238021588, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756605238021669, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756605238021741, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756605238021905, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238022270, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238022479, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238022744, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238022955, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238023159, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238023371, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238023569, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238023798, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238024298, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238024524, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238024718, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238024922, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238025189, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238025665, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238026059, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756605238026196, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756605238026783, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756605238026917, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238027209, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238027295, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238027392, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238027501, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238027759, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238028261, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238028619, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238028997, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238029380, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238029502, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756605238029808, "dur": 8878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238010369, "dur": 10359, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238021025, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1756605238021024, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_A2B6EC8E55EE003B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756605238021126, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_A2B6EC8E55EE003B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756605238021247, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756605238021504, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1756605238021739, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1756605238021862, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238022322, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238022543, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238022820, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238023024, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238023228, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238023435, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238023649, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238023843, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238024496, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\_Deprecated\\CollabPlugin.cs"}}, {"pid": 12345, "tid": 10, "ts": 1756605238024326, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238025088, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238025304, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238025676, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238026080, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238026765, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1756605238026917, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238027207, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238027300, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238027398, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238027517, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238027780, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238028253, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238028600, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238029061, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238029375, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238029484, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238029788, "dur": 8609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756605238038397, "dur": 230, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238010393, "dur": 10347, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238020803, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_F1A60CD50DBC0DBD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238021033, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756605238021032, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_76CD42B8F77F64CB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238021136, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_76CD42B8F77F64CB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238021256, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238022090, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756605238022620, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756605238022755, "dur": 487, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756605238023885, "dur": 296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 11, "ts": 1756605238021328, "dur": 3739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756605238025122, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238025250, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756605238025683, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238025759, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756605238026078, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238026203, "dur": 1015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756605238027324, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238027448, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756605238028241, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756605238028324, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756605238028617, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238029006, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238029362, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238029485, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756605238029796, "dur": 8867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238010431, "dur": 10318, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238020904, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_1904DB9DCBD3120A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756605238021029, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1756605238021013, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_EFCB9BAB71734E8D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756605238021140, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_0014B637A0C5589A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756605238021395, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1756605238021739, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756605238021897, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238022283, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238022507, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238022783, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238023009, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238023213, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238023415, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238023627, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238024490, "dur": 570, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsConverter.cs"}}, {"pid": 12345, "tid": 12, "ts": 1756605238023821, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238025146, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238025201, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238025688, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238026072, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756605238026241, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756605238026801, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238026938, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238027200, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238027299, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238027416, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238027510, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238027766, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238028251, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238028593, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238029016, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238029429, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238029483, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756605238029792, "dur": 8908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238010453, "dur": 10308, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238020908, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238021018, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1756605238021017, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_DA2D9EF91183F51C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756605238021216, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1756605238021736, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1756605238021811, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238022304, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238022525, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238022808, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238023018, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238023252, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238023464, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238023660, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238023874, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238024361, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238024559, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238024641, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238024896, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238025126, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238025263, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238025673, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238026104, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238026784, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1756605238026889, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238026944, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238027203, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238027303, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238027421, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238027500, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238027777, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238028245, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238028602, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238029054, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238029383, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238029487, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756605238029812, "dur": 8880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238010477, "dur": 10299, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238020782, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_5AF89BFBD08F2232.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756605238021053, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238021132, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756605238021298, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756605238021379, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1756605238021557, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1756605238021737, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1756605238021825, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238022073, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238022686, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238022910, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238023118, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238023339, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238023691, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Unity\\UnityObjectOwnershipUtility.cs"}}, {"pid": 12345, "tid": 14, "ts": 1756605238023552, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238024244, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238024439, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238024712, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238024903, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238025125, "dur": 124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238025249, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238025673, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238026076, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756605238026221, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756605238026698, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238026784, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756605238026955, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238027190, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238027316, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238027393, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238027499, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238027750, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238028257, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238028603, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238029046, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238029393, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238029499, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756605238029805, "dur": 8873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238010500, "dur": 10298, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238020812, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756605238020804, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_8ABDAFDA339F2ED9.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756605238021041, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238021123, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1756605238021343, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756605238021510, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756605238021785, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238022214, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Move\\MoveItemModeReplace.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756605238022007, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238022882, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238023084, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238023315, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238023520, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238023736, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238024219, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238024404, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238024624, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238024828, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238025014, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238025282, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238025674, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238026069, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756605238026213, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756605238026775, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1756605238026928, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238027198, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238027305, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238027395, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238027498, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238027652, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238027756, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238028263, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238028591, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238029021, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238029422, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238029488, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756605238029807, "dur": 8911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238010532, "dur": 10277, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238020817, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756605238020810, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_A35037EF751D04E0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756605238020912, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238021029, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\2022.3.38f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1756605238021012, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_228A5EAFFAAD9BEB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756605238021268, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1756605238021558, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1756605238021853, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238022328, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238022530, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238022815, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238023026, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238023221, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238023453, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238023675, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238024401, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238024642, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238024929, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238025237, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238025672, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238026078, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756605238026217, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756605238026639, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238026741, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756605238026919, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238027193, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238027296, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238027394, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238027520, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238027773, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238028243, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238028585, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238028995, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238029364, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238029484, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756605238029827, "dur": 8879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756605238437023, "dur": 1481, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35676, "tid": 478, "ts": 1756605241079725, "dur": 2668, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 35676, "tid": 478, "ts": 1756605241084492, "dur": 30, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 35676, "tid": 478, "ts": 1756605241084847, "dur": 23, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 35676, "tid": 478, "ts": 1756605241082427, "dur": 2061, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 35676, "tid": 478, "ts": 1756605241084543, "dur": 303, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 35676, "tid": 478, "ts": 1756605241084894, "dur": 626, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 35676, "tid": 478, "ts": 1756605241076708, "dur": 9495, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}