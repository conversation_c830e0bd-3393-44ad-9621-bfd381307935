%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!91 &9100000
AnimatorController:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: ThirdPersonAnimatorController
  serializedVersion: 5
  m_AnimatorParameters:
  - m_Name: Forward
    m_Type: 1
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 0}
  - m_Name: Turn
    m_Type: 1
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 0}
  - m_Name: OnGround
    m_Type: 4
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 1
    m_Controller: {fileID: 0}
  m_AnimatorLayers:
  - serializedVersion: 5
    m_Name: Base Layer
    m_StateMachine: {fileID: 110700000}
    m_Mask: {fileID: 0}
    m_Motions: []
    m_Behaviours: []
    m_BlendingMode: 0
    m_SyncedLayerIndex: -1
    m_DefaultWeight: 0
    m_IKPass: 1
    m_SyncedLayerAffectsTiming: 0
    m_Controller: {fileID: 9100000}
--- !u!206 &20600000
BlendTree:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Blend Tree
  m_Childs:
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: dffa50cfe77e0434bbfa71245b3dd529, type: 3}
    m_Threshold: 0
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: 6fb3851da6a6f5948ab6892bee8ba920, type: 3}
    m_Threshold: 0.0952381
    m_Position: {x: 0.5, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400006, guid: 6fb3851da6a6f5948ab6892bee8ba920, type: 3}
    m_Threshold: 0.1904762
    m_Position: {x: 1, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400010, guid: 6fb3851da6a6f5948ab6892bee8ba920, type: 3}
    m_Threshold: 0.2857143
    m_Position: {x: -0.5, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400014, guid: 6fb3851da6a6f5948ab6892bee8ba920, type: 3}
    m_Threshold: 0.3809524
    m_Position: {x: -1, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: b1a5e04ae51004842aba06704a6c2903, type: 3}
    m_Threshold: 0.42857143
    m_Position: {x: 0, y: 0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400002, guid: bb141fc9a700c9c4ca7e6dadb8acf24b, type: 3}
    m_Threshold: 0.47619048
    m_Position: {x: 1, y: 0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: 1c52c953c83c2254a9fa72d50250f028, type: 3}
    m_Threshold: 0.52380955
    m_Position: {x: 0.5, y: 0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: bb141fc9a700c9c4ca7e6dadb8acf24b, type: 3}
    m_Threshold: 0.61904764
    m_Position: {x: -1, y: 0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400002, guid: 1c52c953c83c2254a9fa72d50250f028, type: 3}
    m_Threshold: 0.6666667
    m_Position: {x: -0.5, y: 0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: 1cb8ed3cbba15f0479fbae54e0a963df, type: 3}
    m_Threshold: 0.7619048
    m_Position: {x: 0, y: 1}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: f2bed5dc5afacff44a00de8daae9703b, type: 3}
    m_Threshold: 0.8095238
    m_Position: {x: 1, y: 1}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400002, guid: f2bed5dc5afacff44a00de8daae9703b, type: 3}
    m_Threshold: 0.85714287
    m_Position: {x: -1, y: 1}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: 1062212255550964e974f3ffb3cbaae3, type: 3}
    m_Threshold: 0.9047619
    m_Position: {x: 0.5, y: 1}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400002, guid: 1062212255550964e974f3ffb3cbaae3, type: 3}
    m_Threshold: 0.95238096
    m_Position: {x: -0.5, y: 1}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  m_BlendParameter: Turn
  m_BlendParameterY: Forward
  m_MinThreshold: 0
  m_MaxThreshold: 0.95238096
  m_UseAutomaticThresholds: 0
  m_NormalizedBlendValues: 0
  m_BlendType: 3
--- !u!206 &20600002
BlendTree:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Idle
  m_Childs:
  - serializedVersion: 2
    m_Motion: {fileID: 7400010, guid: 4ee731d726c3dd34eb36806ea0d8fe98, type: 3}
    m_Threshold: -1
    m_Position: {x: 0, y: 0}
    m_TimeScale: 2
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400014, guid: e38eb300eb4745b4db509a224a99bbe1, type: 3}
    m_Threshold: 0
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: 4ee731d726c3dd34eb36806ea0d8fe98, type: 3}
    m_Threshold: 1
    m_Position: {x: 0, y: 0}
    m_TimeScale: 2
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  m_BlendParameter: Turn
  m_BlendParameterY: Blend
  m_MinThreshold: -1
  m_MaxThreshold: 1
  m_UseAutomaticThresholds: 0
  m_NormalizedBlendValues: 0
  m_BlendType: 0
--- !u!206 &20600004
BlendTree:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Walk
  m_Childs:
  - serializedVersion: 2
    m_Motion: {fileID: 7400002, guid: 6da89662649b53c49b06616f51157b48, type: 3}
    m_Threshold: -1
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: 24c848a6dbf95e848950ca5403a1191e, type: 3}
    m_Threshold: 0
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: 6da89662649b53c49b06616f51157b48, type: 3}
    m_Threshold: 1
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  m_BlendParameter: Turn
  m_BlendParameterY: Blend
  m_MinThreshold: -1
  m_MaxThreshold: 1
  m_UseAutomaticThresholds: 0
  m_NormalizedBlendValues: 0
  m_BlendType: 0
--- !u!206 &20600006
BlendTree:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Run
  m_Childs:
  - serializedVersion: 2
    m_Motion: {fileID: 7400026, guid: ccb909e390d7be24e9107d33119a0eaa, type: 3}
    m_Threshold: -1
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400024, guid: ccb909e390d7be24e9107d33119a0eaa, type: 3}
    m_Threshold: 0
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400022, guid: ccb909e390d7be24e9107d33119a0eaa, type: 3}
    m_Threshold: 1
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  m_BlendParameter: Turn
  m_BlendParameterY: Blend
  m_MinThreshold: -1
  m_MaxThreshold: 1
  m_UseAutomaticThresholds: 0
  m_NormalizedBlendValues: 0
  m_BlendType: 0
--- !u!206 &20608386
BlendTree:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Idle
  m_Childs:
  - serializedVersion: 2
    m_Motion: {fileID: 7400002, guid: 98e8896e12d39bb41a5a74e9ae897a64, type: 3}
    m_Threshold: -1
    m_Position: {x: 0, y: 0}
    m_TimeScale: 2
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: 11cd8118786c19d49a6bf4fc939ad434, type: 3}
    m_Threshold: 0
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: 98e8896e12d39bb41a5a74e9ae897a64, type: 3}
    m_Threshold: 1
    m_Position: {x: 0, y: 0}
    m_TimeScale: 2
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  m_BlendParameter: Turn
  m_BlendParameterY: Blend
  m_MinThreshold: -1
  m_MaxThreshold: 1
  m_UseAutomaticThresholds: 0
  m_NormalizedBlendValues: 0
  m_BlendType: 0
--- !u!206 &20610787
BlendTree:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Blend Tree
  m_Childs:
  - serializedVersion: 2
    m_Motion: {fileID: 7400002, guid: f03e10c73f30b4ab4aa8ea8f1cc16d36, type: 3}
    m_Threshold: 0
    m_Position: {x: 0, y: -1}
    m_TimeScale: 0.1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400004, guid: 51dd2e4c869794f75a0df7d54b210214, type: 3}
    m_Threshold: 5
    m_Position: {x: 5, y: -1}
    m_TimeScale: 0.1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: 51dd2e4c869794f75a0df7d54b210214, type: 3}
    m_Threshold: 15
    m_Position: {x: 5, y: 1}
    m_TimeScale: 0.1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400004, guid: 0d9d26e2162aa4d11ab075b34c029673, type: 3}
    m_Threshold: 20
    m_Position: {x: -5, y: 0}
    m_TimeScale: 0.1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400004, guid: f03e10c73f30b4ab4aa8ea8f1cc16d36, type: 3}
    m_Threshold: 25
    m_Position: {x: 0, y: 1}
    m_TimeScale: 0.1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400006, guid: 0d9d26e2162aa4d11ab075b34c029673, type: 3}
    m_Threshold: 35
    m_Position: {x: 5, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400008, guid: 0d9d26e2162aa4d11ab075b34c029673, type: 3}
    m_Threshold: 40
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  m_BlendParameter: Jump
  m_BlendParameterY: JumpLeg
  m_MinThreshold: 0
  m_MaxThreshold: 40
  m_UseAutomaticThresholds: 0
  m_NormalizedBlendValues: 0
  m_BlendType: 3
--- !u!206 &20631403
BlendTree:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Walk
  m_Childs:
  - serializedVersion: 2
    m_Motion: {fileID: 7400002, guid: 1da5f9c54c49bfc488819dd2df8bb228, type: 3}
    m_Threshold: -1
    m_Position: {x: 0, y: 0}
    m_TimeScale: 2
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: c869773dc0bdfe042a8293344c186eaf, type: 3}
    m_Threshold: 0
    m_Position: {x: 0, y: 0}
    m_TimeScale: 2
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 7400000, guid: 1da5f9c54c49bfc488819dd2df8bb228, type: 3}
    m_Threshold: 1
    m_Position: {x: 0, y: 0}
    m_TimeScale: 2
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  m_BlendParameter: Turn
  m_BlendParameterY: Blend
  m_MinThreshold: -1
  m_MaxThreshold: 1
  m_UseAutomaticThresholds: 0
  m_NormalizedBlendValues: 0
  m_BlendType: 0
--- !u!206 &20659883
BlendTree:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Blend Tree
  m_Childs:
  - serializedVersion: 2
    m_Motion: {fileID: 20608386}
    m_Threshold: 0
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: 20631403}
    m_Threshold: 1
    m_Position: {x: 0, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: 
    m_Mirror: 0
  m_BlendParameter: Forward
  m_BlendParameterY: Blend
  m_MinThreshold: 0
  m_MaxThreshold: 1
  m_UseAutomaticThresholds: 1
  m_NormalizedBlendValues: 0
  m_BlendType: 0
--- !u!206 &20683409
BlendTree:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Blend Tree
  m_Childs: []
  m_BlendParameter: Forward
  m_BlendParameterY: Forward
  m_MinThreshold: 0
  m_MaxThreshold: 1
  m_UseAutomaticThresholds: 1
  m_NormalizedBlendValues: 0
  m_BlendType: 0
--- !u!1101 &110167223
AnimatorStateTransition:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 2
    m_ConditionEvent: Crouch
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 110298501}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.101033315
  m_TransitionOffset: 0
  m_ExitTime: 0.9
  m_HasExitTime: 0
  m_HasFixedDuration: 0
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1102 &110298501
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Grounded
  m_Speed: 1
  m_CycleOffset: 0
  m_Transitions:
  - {fileID: 1101866826946749930}
  m_StateMachineBehaviours: []
  m_Position: {x: 588, y: 96, z: 0}
  m_IKOnFeet: 1
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_Motion: {fileID: 20600000}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
--- !u!1107 &110700000
AnimatorStateMachine:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Base Layer
  m_ChildStates:
  - serializedVersion: 1
    m_State: {fileID: 110298501}
    m_Position: {x: 588, y: 96, z: 0}
  - serializedVersion: 1
    m_State: {fileID: 1102219825057348516}
    m_Position: {x: 588, y: 228, z: 0}
  m_ChildStateMachines: []
  m_AnyStateTransitions: []
  m_EntryTransitions: []
  m_StateMachineTransitions:
    data:
      first: {fileID: 110700000}
      second: []
  m_StateMachineBehaviours: []
  m_AnyStatePosition: {x: 50, y: 20, z: 0}
  m_EntryPosition: {x: 36, y: 108, z: 0}
  m_ExitPosition: {x: 852, y: 96, z: 0}
  m_ParentStateMachinePosition: {x: 800, y: 20, z: 0}
  m_DefaultState: {fileID: 110298501}
--- !u!1101 &1101504213677693024
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: OnGround
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 110298501}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 2
  m_TransitionOffset: 0
  m_ExitTime: 0.75
  m_HasExitTime: 0
  m_HasFixedDuration: 0
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &1101866826946749930
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 2
    m_ConditionEvent: OnGround
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 1102219825057348516}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.33
  m_TransitionOffset: 0
  m_ExitTime: 0.7470205
  m_HasExitTime: 0
  m_HasFixedDuration: 0
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1102 &1102219825057348516
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: InAir
  m_Speed: 1
  m_CycleOffset: 0
  m_Transitions:
  - {fileID: 1101504213677693024}
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_Motion: {fileID: 7400008, guid: 0d9d26e2162aa4d11ab075b34c029673, type: 3}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
