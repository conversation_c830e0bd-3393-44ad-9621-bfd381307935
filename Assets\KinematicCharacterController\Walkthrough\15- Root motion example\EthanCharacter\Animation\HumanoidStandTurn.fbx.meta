fileFormatVersion: 2
guid: 6fb3851da6a6f5948ab6892bee8ba920
ModelImporter:
  serializedVersion: 18
  fileIDToRecycleName:
    100000: //RootNode
    100002: Character_Ctrl:Reference
    100004: Chest
    100006: ChestEndEffector
    100008: ChestOriginEffector
    100010: chestProxy_geo
    100012: Head
    100014: Head 1
    100016: HeadEffector
    100018: headProxy_geo
    100020: HeadTop_End
    100022: Hips
    100024: Hips 1
    100026: HipsEffector
    100028: Jaw
    100030: JawEND
    100032: jawProxy_geo
    100034: l_ankleProxy_geo
    100036: l_ballProxy_geo
    100038: l_clavicleProxy_geo
    100040: l_erbowProxy_geo
    100042: l_hipProxy_geo
    100044: l_indexProxy_01_geo
    100046: l_indexProxy_02_geo
    100048: l_indexProxy_03_geo
    100050: l_kneeProxy_geo
    100052: l_middleProxy_01_geo
    100054: l_middleProxy_02_geo
    100056: l_middleProxy_03_geo
    100058: l_pinkyProxy_01_geo
    100060: l_pinkyProxy_02_geo
    100062: l_pinkyProxy_03_geo
    100064: l_ringProxy_01_geo
    100066: l_ringProxy_02_geo
    100068: l_ringProxy_03_geo
    100070: l_shourderProxy_geo
    100072: l_thumbProxy_01_geo
    100074: l_thumbProxy_02_geo
    100076: l_thumbProxy_03_geo
    100078: l_UNI_eye
    100080: l_wristProxy_geo
    100082: LeftAnkleEffector
    100084: LeftArm
    100086: LeftArm 1
    100088: LeftCheek
    100090: LeftElbowEffector
    100092: LeftEye
    100094: LeftEyelidLower
    100096: LeftEyelidUpper
    100098: LeftFoot
    100100: LeftFoot 1
    100102: LeftForeArm
    100104: LeftForeArm 1
    100106: LeftHand
    100108: LeftHand 1
    100110: LeftHandIndex1
    100112: LeftHandIndex13
    100114: LeftHandIndex17
    100116: LeftHandIndex2
    100118: LeftHandIndex3
    100120: LeftHandIndex4
    100122: LeftHandIndex5
    100124: LeftHandIndex6
    100126: LeftHandIndexEffector
    100128: LeftHandMiddle1
    100130: LeftHandMiddle13
    100132: LeftHandMiddle17
    100134: LeftHandMiddle2
    100136: LeftHandMiddle3
    100138: LeftHandMiddle4
    100140: LeftHandMiddle5
    100142: LeftHandMiddle6
    100144: LeftHandMiddleEffector
    100146: LeftHandPinky1
    100148: LeftHandPinky13
    100150: LeftHandPinky17
    100152: LeftHandPinky2
    100154: LeftHandPinky3
    100156: LeftHandPinky4
    100158: LeftHandPinky5
    100160: LeftHandPinky6
    100162: LeftHandPinkyEffector
    100164: LeftHandRing1
    100166: LeftHandRing13
    100168: LeftHandRing17
    100170: LeftHandRing2
    100172: LeftHandRing3
    100174: LeftHandRing4
    100176: LeftHandRing5
    100178: LeftHandRing6
    100180: LeftHandRingEffector
    100182: LeftHandThumb1
    100184: LeftHandThumb13
    100186: LeftHandThumb17
    100188: LeftHandThumb2
    100190: LeftHandThumb3
    100192: LeftHandThumb4
    100194: LeftHandThumb5
    100196: LeftHandThumb6
    100198: LeftHandThumbEffector
    100200: LeftHipEffector
    100202: LeftInnerBrow
    100204: LeftIOuterBrow
    100206: LeftKneeEffector
    100208: LeftLeg
    100210: LeftLeg 1
    100212: LeftLipCorner
    100214: LeftLipLower
    100216: LeftLipUpper
    100218: LeftNostril
    100220: LeftShoulder
    100222: LeftShoulder 1
    100224: LeftShoulderEffector
    100226: LeftToes
    100228: LeftUpLeg
    100230: LeftUpLeg 1
    100232: LeftWristEffector
    100234: LToeBase_End2
    100236: LToeBase_End3
    100238: Neck
    100240: Neck 1
    100242: neckProxy_geo
    100244: pelvisProxy_geo
    100246: r_ankleProxy_geo
    100248: r_ballProxy_geo
    100250: r_clavicleProxy_geo
    100252: r_erbowProxy_geo
    100254: r_hipProxy_geo
    100256: r_indexProxy_01_geo
    100258: r_indexProxy_02_geo
    100260: r_indexProxy_03_geo
    100262: r_kneeProxy_geo
    100264: r_middleProxy_01_geo
    100266: r_middleProxy_02_geo
    100268: r_middleProxy_03_geo
    100270: r_pinkyProxy_01_geo
    100272: r_pinkyProxy_02_geo
    100274: r_pinkyProxy_03_geo
    100276: r_ringProxy_01_geo
    100278: r_ringProxy_02_geo
    100280: r_ringProxy_03_geo
    100282: r_shourderProxy_geo
    100284: r_thumbProxy_01_geo
    100286: r_thumbProxy_02_geo
    100288: r_thumbProxy_03_geo
    100290: r_UNI_eye
    100292: r_wristProxy_geo
    100294: Reference
    100296: RightAnkleEffector
    100298: RightArm
    100300: RightArm 1
    100302: RightCheek
    100304: RightElbowEffector
    100306: RightEye
    100308: RightEyelidLower
    100310: RightEyelidUpper
    100312: RightFoot
    100314: RightFoot 1
    100316: RightForeArm
    100318: RightForeArm 1
    100320: RightHand
    100322: RightHand 1
    100324: RightHandIndex1
    100326: RightHandIndex2
    100328: RightHandIndex3
    100330: RightHandIndex4
    100332: RightHandIndex5
    100334: RightHandIndex6
    100336: RightHandIndexEffector
    100338: RightHandMiddle1
    100340: RightHandMiddle2
    100342: RightHandMiddle3
    100344: RightHandMiddle4
    100346: RightHandMiddle5
    100348: RightHandMiddle6
    100350: RightHandMiddleEffector
    100352: RightHandPinky1
    100354: RightHandPinky2
    100356: RightHandPinky3
    100358: RightHandPinky4
    100360: RightHandPinky5
    100362: RightHandPinky6
    100364: RightHandPinkyEffector
    100366: RightHandRing1
    100368: RightHandRing2
    100370: RightHandRing3
    100372: RightHandRing4
    100374: RightHandRing5
    100376: RightHandRing6
    100378: RightHandRingEffector
    100380: RightHandThumb1
    100382: RightHandThumb2
    100384: RightHandThumb3
    100386: RightHandThumb4
    100388: RightHandThumb5
    100390: RightHandThumb6
    100392: RightHandThumbEffector
    100394: RightHipEffector
    100396: RightInnerBrow
    100398: RightIOuterBrow
    100400: RightKneeEffector
    100402: RightLeg
    100404: RightLeg 1
    100406: RightLipCorner
    100408: RightLipLower
    100410: RightLipUpper
    100412: RightNostril
    100414: RightShoulder
    100416: RightShoulder 1
    100418: RightShoulderEffector
    100420: RightToes
    100422: RightUpLeg
    100424: RightUpLeg 1
    100426: RightWristEffector
    100428: Spine
    100430: Spine 1
    100432: Spine1
    100434: spineProxy_geo
    100436: TongueBack
    100438: TongueTip
    100440: UNI_01_Lower_teethProxy
    100442: UNI_01_TongueBaseProxy
    100444: UNI_01_TongueTipProxy
    100446: UNI_01_Upper_teethProxy
    400000: //RootNode
    400002: Character_Ctrl:Reference
    400004: Chest
    400006: ChestEndEffector
    400008: ChestOriginEffector
    400010: chestProxy_geo
    400012: Head
    400014: Head 1
    400016: HeadEffector
    400018: headProxy_geo
    400020: HeadTop_End
    400022: Hips
    400024: Hips 1
    400026: HipsEffector
    400028: Jaw
    400030: JawEND
    400032: jawProxy_geo
    400034: l_ankleProxy_geo
    400036: l_ballProxy_geo
    400038: l_clavicleProxy_geo
    400040: l_erbowProxy_geo
    400042: l_hipProxy_geo
    400044: l_indexProxy_01_geo
    400046: l_indexProxy_02_geo
    400048: l_indexProxy_03_geo
    400050: l_kneeProxy_geo
    400052: l_middleProxy_01_geo
    400054: l_middleProxy_02_geo
    400056: l_middleProxy_03_geo
    400058: l_pinkyProxy_01_geo
    400060: l_pinkyProxy_02_geo
    400062: l_pinkyProxy_03_geo
    400064: l_ringProxy_01_geo
    400066: l_ringProxy_02_geo
    400068: l_ringProxy_03_geo
    400070: l_shourderProxy_geo
    400072: l_thumbProxy_01_geo
    400074: l_thumbProxy_02_geo
    400076: l_thumbProxy_03_geo
    400078: l_UNI_eye
    400080: l_wristProxy_geo
    400082: LeftAnkleEffector
    400084: LeftArm
    400086: LeftArm 1
    400088: LeftCheek
    400090: LeftElbowEffector
    400092: LeftEye
    400094: LeftEyelidLower
    400096: LeftEyelidUpper
    400098: LeftFoot
    400100: LeftFoot 1
    400102: LeftForeArm
    400104: LeftForeArm 1
    400106: LeftHand
    400108: LeftHand 1
    400110: LeftHandIndex1
    400112: LeftHandIndex13
    400114: LeftHandIndex17
    400116: LeftHandIndex2
    400118: LeftHandIndex3
    400120: LeftHandIndex4
    400122: LeftHandIndex5
    400124: LeftHandIndex6
    400126: LeftHandIndexEffector
    400128: LeftHandMiddle1
    400130: LeftHandMiddle13
    400132: LeftHandMiddle17
    400134: LeftHandMiddle2
    400136: LeftHandMiddle3
    400138: LeftHandMiddle4
    400140: LeftHandMiddle5
    400142: LeftHandMiddle6
    400144: LeftHandMiddleEffector
    400146: LeftHandPinky1
    400148: LeftHandPinky13
    400150: LeftHandPinky17
    400152: LeftHandPinky2
    400154: LeftHandPinky3
    400156: LeftHandPinky4
    400158: LeftHandPinky5
    400160: LeftHandPinky6
    400162: LeftHandPinkyEffector
    400164: LeftHandRing1
    400166: LeftHandRing13
    400168: LeftHandRing17
    400170: LeftHandRing2
    400172: LeftHandRing3
    400174: LeftHandRing4
    400176: LeftHandRing5
    400178: LeftHandRing6
    400180: LeftHandRingEffector
    400182: LeftHandThumb1
    400184: LeftHandThumb13
    400186: LeftHandThumb17
    400188: LeftHandThumb2
    400190: LeftHandThumb3
    400192: LeftHandThumb4
    400194: LeftHandThumb5
    400196: LeftHandThumb6
    400198: LeftHandThumbEffector
    400200: LeftHipEffector
    400202: LeftInnerBrow
    400204: LeftIOuterBrow
    400206: LeftKneeEffector
    400208: LeftLeg
    400210: LeftLeg 1
    400212: LeftLipCorner
    400214: LeftLipLower
    400216: LeftLipUpper
    400218: LeftNostril
    400220: LeftShoulder
    400222: LeftShoulder 1
    400224: LeftShoulderEffector
    400226: LeftToes
    400228: LeftUpLeg
    400230: LeftUpLeg 1
    400232: LeftWristEffector
    400234: LToeBase_End2
    400236: LToeBase_End3
    400238: Neck
    400240: Neck 1
    400242: neckProxy_geo
    400244: pelvisProxy_geo
    400246: r_ankleProxy_geo
    400248: r_ballProxy_geo
    400250: r_clavicleProxy_geo
    400252: r_erbowProxy_geo
    400254: r_hipProxy_geo
    400256: r_indexProxy_01_geo
    400258: r_indexProxy_02_geo
    400260: r_indexProxy_03_geo
    400262: r_kneeProxy_geo
    400264: r_middleProxy_01_geo
    400266: r_middleProxy_02_geo
    400268: r_middleProxy_03_geo
    400270: r_pinkyProxy_01_geo
    400272: r_pinkyProxy_02_geo
    400274: r_pinkyProxy_03_geo
    400276: r_ringProxy_01_geo
    400278: r_ringProxy_02_geo
    400280: r_ringProxy_03_geo
    400282: r_shourderProxy_geo
    400284: r_thumbProxy_01_geo
    400286: r_thumbProxy_02_geo
    400288: r_thumbProxy_03_geo
    400290: r_UNI_eye
    400292: r_wristProxy_geo
    400294: Reference
    400296: RightAnkleEffector
    400298: RightArm
    400300: RightArm 1
    400302: RightCheek
    400304: RightElbowEffector
    400306: RightEye
    400308: RightEyelidLower
    400310: RightEyelidUpper
    400312: RightFoot
    400314: RightFoot 1
    400316: RightForeArm
    400318: RightForeArm 1
    400320: RightHand
    400322: RightHand 1
    400324: RightHandIndex1
    400326: RightHandIndex2
    400328: RightHandIndex3
    400330: RightHandIndex4
    400332: RightHandIndex5
    400334: RightHandIndex6
    400336: RightHandIndexEffector
    400338: RightHandMiddle1
    400340: RightHandMiddle2
    400342: RightHandMiddle3
    400344: RightHandMiddle4
    400346: RightHandMiddle5
    400348: RightHandMiddle6
    400350: RightHandMiddleEffector
    400352: RightHandPinky1
    400354: RightHandPinky2
    400356: RightHandPinky3
    400358: RightHandPinky4
    400360: RightHandPinky5
    400362: RightHandPinky6
    400364: RightHandPinkyEffector
    400366: RightHandRing1
    400368: RightHandRing2
    400370: RightHandRing3
    400372: RightHandRing4
    400374: RightHandRing5
    400376: RightHandRing6
    400378: RightHandRingEffector
    400380: RightHandThumb1
    400382: RightHandThumb2
    400384: RightHandThumb3
    400386: RightHandThumb4
    400388: RightHandThumb5
    400390: RightHandThumb6
    400392: RightHandThumbEffector
    400394: RightHipEffector
    400396: RightInnerBrow
    400398: RightIOuterBrow
    400400: RightKneeEffector
    400402: RightLeg
    400404: RightLeg 1
    400406: RightLipCorner
    400408: RightLipLower
    400410: RightLipUpper
    400412: RightNostril
    400414: RightShoulder
    400416: RightShoulder 1
    400418: RightShoulderEffector
    400420: RightToes
    400422: RightUpLeg
    400424: RightUpLeg 1
    400426: RightWristEffector
    400428: Spine
    400430: Spine 1
    400432: Spine1
    400434: spineProxy_geo
    400436: TongueBack
    400438: TongueTip
    400440: UNI_01_Lower_teethProxy
    400442: UNI_01_TongueBaseProxy
    400444: UNI_01_TongueTipProxy
    400446: UNI_01_Upper_teethProxy
    2300000: chestProxy_geo
    2300002: headProxy_geo
    2300004: jawProxy_geo
    2300006: l_ankleProxy_geo
    2300008: l_ballProxy_geo
    2300010: l_clavicleProxy_geo
    2300012: l_erbowProxy_geo
    2300014: l_hipProxy_geo
    2300016: l_indexProxy_01_geo
    2300018: l_indexProxy_02_geo
    2300020: l_indexProxy_03_geo
    2300022: l_kneeProxy_geo
    2300024: l_middleProxy_01_geo
    2300026: l_middleProxy_02_geo
    2300028: l_middleProxy_03_geo
    2300030: l_pinkyProxy_01_geo
    2300032: l_pinkyProxy_02_geo
    2300034: l_pinkyProxy_03_geo
    2300036: l_ringProxy_01_geo
    2300038: l_ringProxy_02_geo
    2300040: l_ringProxy_03_geo
    2300042: l_shourderProxy_geo
    2300044: l_thumbProxy_01_geo
    2300046: l_thumbProxy_02_geo
    2300048: l_thumbProxy_03_geo
    2300050: l_UNI_eye
    2300052: l_wristProxy_geo
    2300054: neckProxy_geo
    2300056: pelvisProxy_geo
    2300058: r_ankleProxy_geo
    2300060: r_ballProxy_geo
    2300062: r_clavicleProxy_geo
    2300064: r_erbowProxy_geo
    2300066: r_hipProxy_geo
    2300068: r_indexProxy_01_geo
    2300070: r_indexProxy_02_geo
    2300072: r_indexProxy_03_geo
    2300074: r_kneeProxy_geo
    2300076: r_middleProxy_01_geo
    2300078: r_middleProxy_02_geo
    2300080: r_middleProxy_03_geo
    2300082: r_pinkyProxy_01_geo
    2300084: r_pinkyProxy_02_geo
    2300086: r_pinkyProxy_03_geo
    2300088: r_ringProxy_01_geo
    2300090: r_ringProxy_02_geo
    2300092: r_ringProxy_03_geo
    2300094: r_shourderProxy_geo
    2300096: r_thumbProxy_01_geo
    2300098: r_thumbProxy_02_geo
    2300100: r_thumbProxy_03_geo
    2300102: r_UNI_eye
    2300104: r_wristProxy_geo
    2300106: spineProxy_geo
    2300108: UNI_01_Lower_teethProxy
    2300110: UNI_01_TongueBaseProxy
    2300112: UNI_01_TongueTipProxy
    2300114: UNI_01_Upper_teethProxy
    3300000: chestProxy_geo
    3300002: headProxy_geo
    3300004: jawProxy_geo
    3300006: l_ankleProxy_geo
    3300008: l_ballProxy_geo
    3300010: l_clavicleProxy_geo
    3300012: l_erbowProxy_geo
    3300014: l_hipProxy_geo
    3300016: l_indexProxy_01_geo
    3300018: l_indexProxy_02_geo
    3300020: l_indexProxy_03_geo
    3300022: l_kneeProxy_geo
    3300024: l_middleProxy_01_geo
    3300026: l_middleProxy_02_geo
    3300028: l_middleProxy_03_geo
    3300030: l_pinkyProxy_01_geo
    3300032: l_pinkyProxy_02_geo
    3300034: l_pinkyProxy_03_geo
    3300036: l_ringProxy_01_geo
    3300038: l_ringProxy_02_geo
    3300040: l_ringProxy_03_geo
    3300042: l_shourderProxy_geo
    3300044: l_thumbProxy_01_geo
    3300046: l_thumbProxy_02_geo
    3300048: l_thumbProxy_03_geo
    3300050: l_UNI_eye
    3300052: l_wristProxy_geo
    3300054: neckProxy_geo
    3300056: pelvisProxy_geo
    3300058: r_ankleProxy_geo
    3300060: r_ballProxy_geo
    3300062: r_clavicleProxy_geo
    3300064: r_erbowProxy_geo
    3300066: r_hipProxy_geo
    3300068: r_indexProxy_01_geo
    3300070: r_indexProxy_02_geo
    3300072: r_indexProxy_03_geo
    3300074: r_kneeProxy_geo
    3300076: r_middleProxy_01_geo
    3300078: r_middleProxy_02_geo
    3300080: r_middleProxy_03_geo
    3300082: r_pinkyProxy_01_geo
    3300084: r_pinkyProxy_02_geo
    3300086: r_pinkyProxy_03_geo
    3300088: r_ringProxy_01_geo
    3300090: r_ringProxy_02_geo
    3300092: r_ringProxy_03_geo
    3300094: r_shourderProxy_geo
    3300096: r_thumbProxy_01_geo
    3300098: r_thumbProxy_02_geo
    3300100: r_thumbProxy_03_geo
    3300102: r_UNI_eye
    3300104: r_wristProxy_geo
    3300106: spineProxy_geo
    3300108: UNI_01_Lower_teethProxy
    3300110: UNI_01_TongueBaseProxy
    3300112: UNI_01_TongueTipProxy
    3300114: UNI_01_Upper_teethProxy
    4300000: l_UNI_eye
    4300002: r_UNI_eye
    4300004: UNI_01_TongueBaseProxy
    4300006: UNI_01_TongueTipProxy
    4300008: UNI_01_Lower_teethProxy
    4300010: jawProxy_geo
    4300012: headProxy_geo
    4300014: UNI_01_Upper_teethProxy
    4300016: neckProxy_geo
    4300018: r_pinkyProxy_03_geo
    4300020: r_pinkyProxy_02_geo
    4300022: r_pinkyProxy_01_geo
    4300024: r_ringProxy_03_geo
    4300026: r_ringProxy_02_geo
    4300028: r_ringProxy_01_geo
    4300030: r_middleProxy_03_geo
    4300032: r_middleProxy_02_geo
    4300034: r_middleProxy_01_geo
    4300036: r_indexProxy_03_geo
    4300038: r_indexProxy_02_geo
    4300040: r_indexProxy_01_geo
    4300042: r_thumbProxy_03_geo
    4300044: r_thumbProxy_02_geo
    4300046: r_thumbProxy_01_geo
    4300048: r_wristProxy_geo
    4300050: r_erbowProxy_geo
    4300052: r_shourderProxy_geo
    4300054: r_clavicleProxy_geo
    4300056: chestProxy_geo
    4300058: l_pinkyProxy_03_geo
    4300060: l_pinkyProxy_02_geo
    4300062: l_pinkyProxy_01_geo
    4300064: l_ringProxy_03_geo
    4300066: l_ringProxy_02_geo
    4300068: l_ringProxy_01_geo
    4300070: l_middleProxy_03_geo
    4300072: l_middleProxy_02_geo
    4300074: l_middleProxy_01_geo
    4300076: l_indexProxy_03_geo
    4300078: l_indexProxy_02_geo
    4300080: l_indexProxy_01_geo
    4300082: l_thumbProxy_03_geo
    4300084: l_thumbProxy_02_geo
    4300086: l_thumbProxy_01_geo
    4300088: l_wristProxy_geo
    4300090: l_erbowProxy_geo
    4300092: l_shourderProxy_geo
    4300094: l_clavicleProxy_geo
    4300096: spineProxy_geo
    4300098: r_ballProxy_geo
    4300100: r_ankleProxy_geo
    4300102: r_kneeProxy_geo
    4300104: r_hipProxy_geo
    4300106: pelvisProxy_geo
    4300108: l_ballProxy_geo
    4300110: l_ankleProxy_geo
    4300112: l_kneeProxy_geo
    4300114: l_hipProxy_geo
    7400000: StandQuarterTurnRight
    7400002: Stand Turn Right A
    7400004: Stand Turn Right C
    7400006: StandHalfTurnRight
    7400008: Stand Turn Left A
    7400010: StandQuarterTurnLeft
    7400012: Stand Turn Left C
    7400014: StandHalfTurnLeft
    9500000: //RootNode
  materials:
    importMaterials: 0
    materialName: 1
    materialSearch: 2
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    optimizeGameObjects: 0
    motionNodeName: 
    pivotNodeName: 
    animationCompression: 0
    animationRotationError: .5
    animationPositionError: .5
    animationScaleError: .5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: StandQuarterTurnRight
      takeName: _97_TO_100_a_U1_M_P_idle_NeutralTO45IdleTONeutralIdle__Fb_p45_No_0_PJ_2
      firstFrame: 284
      lastFrame: 315
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
    - serializedVersion: 16
      name: StandHalfTurnRight
      takeName: _97_TO_100_a_U1_M_P_idle_NeutralTO45IdleTONeutralIdle__Fb_p45_No_0_PJ_2
      firstFrame: 622
      lastFrame: 659
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
    - serializedVersion: 16
      name: StandQuarterTurnLeft
      takeName: _97_TO_100_a_U1_M_P_idle_NeutralTO45IdleTONeutralIdle__Fb_p45_No_0_PJ_2
      firstFrame: 284
      lastFrame: 315
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 1
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
    - serializedVersion: 16
      name: StandHalfTurnLeft
      takeName: _97_TO_100_a_U1_M_P_idle_NeutralTO45IdleTONeutralIdle__Fb_p45_No_0_PJ_2
      firstFrame: 622
      lastFrame: 659
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 1
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: .00999999978
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    splitTangentsAcrossUV: 1
    normalImportMode: 0
    tangentImportMode: 1
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToes
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToes
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftEye
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightEye
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Jaw
      humanName: Jaw
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: HumanoidStandTurn(Clone)
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Hips
      position: {x: .00221104478, y: .960555851, z: .00774985878}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftUpLeg
      position: {x: -.0754494965, y: -.0456640199, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLeg
      position: {x: -.0205504987, y: -.409129977, z: -.000718647963}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftFoot
      position: {x: -.00515299942, y: -.423155904, z: -.0276488513}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftToes
      position: {x: -.00748699997, y: -.0731673017, z: .145427123}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightUpLeg
      position: {x: .0754495338, y: -.0456639901, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLeg
      position: {x: .0205504671, y: -.409130007, z: -.000718647963}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightFoot
      position: {x: .00515299942, y: -.423155904, z: -.0276488513}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightToes
      position: {x: .00748699997, y: -.0731673017, z: .145427495}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Spine
      position: {x: 2.6469779e-25, y: .0922631845, z: .0157713313}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Chest
      position: {x: -0, y: .162540287, z: -.00165605545}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftShoulder
      position: {x: -.0382859968, y: .221622497, z: -.017063085}
      rotation: {x: -.0231811199, y: -.0412411205, z: .155462235, w: .986708343}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftArm
      position: {x: -.100502051, y: 5.68434176e-16, z: -3.330669e-18}
      rotation: {x: .088617675, y: .0276518222, z: -.142930418, w: .985369623}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftForeArm
      position: {x: -.254049301, y: 5.68434176e-16, z: 1.11022296e-17}
      rotation: {x: .124834083, y: .0313581899, z: .00281256856, w: .991677999}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHand
      position: {x: -.24638927, y: 0, z: -1.99840139e-16}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex1
      position: {x: -.0751257986, y: -.00784140453, z: .0326526426}
      rotation: {x: .00607836014, y: -.0167527385, z: .0568730906, w: .998222351}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex2
      position: {x: -.03979728, y: 4.98084046e-05, z: .00118575036}
      rotation: {x: -.0675409213, y: .0152366757, z: .0327178091, w: .997063518}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex3
      position: {x: -.0279684775, y: -6.28122487e-09, z: -5.17186614e-08}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle1
      position: {x: -.0760238245, y: -.00188513438, z: .0101412293}
      rotation: {x: -.00378268166, y: .0447947718, z: .0881895795, w: .995088816}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle2
      position: {x: -.0442804359, y: 4.79887422e-06, z: -.000425400125}
      rotation: {x: -.0125990948, y: -.00755135808, z: .0314779356, w: .999396563}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle3
      position: {x: -.0339648277, y: -1.21979289e-08, z: 3.75648268e-09}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky1
      position: {x: -.0656599477, y: -.00782510638, z: -.0322512463}
      rotation: {x: -.0661177263, y: .0816951618, z: .0931271091, w: .990091801}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky2
      position: {x: -.0308054481, y: -3.0874573e-05, z: -.0014480775}
      rotation: {x: .0469475128, y: -.0211696289, z: .0376872718, w: .9979617}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky3
      position: {x: -.0230640266, y: -6.40258077e-06, z: 1.8332095e-08}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing1
      position: {x: -.0703021064, y: -.00374530931, z: -.0114117917}
      rotation: {x: -.0201800391, y: .0723013356, z: .0900597498, w: .993103564}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing2
      position: {x: -.0431354567, y: -2.08823076e-05, z: -.00223517837}
      rotation: {x: .0182891581, y: -.0256066062, z: .0339722671, w: .998927355}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing3
      position: {x: -.0308355652, y: 7.71035458e-11, z: -1.64932707e-08}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb1
      position: {x: -.0142312413, y: -.0123778246, z: .0255316682}
      rotation: {x: -.102118149, y: -.0509434976, z: -.10264302, w: .988150299}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb2
      position: {x: -.0163739994, y: -.00528999977, z: .0234914087}
      rotation: {x: -.0260635857, y: .0966900364, z: .00360634457, w: .994966686}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb3
      position: {x: -.0254599992, y: -.00763999997, z: .0208330005}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Neck
      position: {x: -0, y: .259009302, z: -.0324132554}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Head
      position: {x: -2.6469779e-25, y: .0830703825, z: .0113267815}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Jaw
      position: {x: 1.73472344e-20, y: .0111267585, z: .0103275431}
      rotation: {x: .219240054, y: -0, z: -0, w: .975670993}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: JawEND
      position: {x: -1.73472344e-20, y: -.0482887588, z: .071851708}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipCorner
      position: {x: -.032843262, y: -.01657876, z: .0661217645}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipLower
      position: {x: -.0142508168, y: -.0216887593, z: .0822406337}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipCorner
      position: {x: .0328399986, y: -.01657876, z: .0661187842}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipLower
      position: {x: .0142508168, y: -.0216887593, z: .0822387859}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: TongueBack
      position: {x: -1.73472344e-20, y: -.022869369, z: .0100954091}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: TongueTip
      position: {x: -1.73472344e-20, y: -.0232788119, z: .0383227095}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftCheek
      position: {x: -.0542440265, y: .0337019488, z: .0594304018}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEye
      position: {x: -.0208482333, y: .0825027004, z: .0554274321}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEyelidLower
      position: {x: -.0356189571, y: .0650736615, z: .076234743}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEyelidUpper
      position: {x: -.0344068967, y: .10060814, z: .0802053064}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftInnerBrow
      position: {x: -.0120626912, y: .118765265, z: .0934668258}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftIOuterBrow
      position: {x: -.0550398715, y: .114825293, z: .061777398}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipUpper
      position: {x: -.0145013221, y: -.00511181122, z: .094618842}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftNostril
      position: {x: -.0178999994, y: .0263128281, z: .0908674002}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightCheek
      position: {x: .0542399958, y: .033702828, z: .0594273992}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEye
      position: {x: .020849999, y: .082502827, z: .0554273985}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEyelidLower
      position: {x: .0356200002, y: .065072827, z: .0762374029}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEyelidUpper
      position: {x: .0344099998, y: .100612827, z: .0802073926}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightInnerBrow
      position: {x: .0120626874, y: .118765265, z: .0934668258}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightIOuterBrow
      position: {x: .0550400019, y: .114822827, z: .061777398}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipUpper
      position: {x: .0145013221, y: -.00510717137, z: .094617404}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightNostril
      position: {x: .0178999994, y: .0263089053, z: .0908706188}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightShoulder
      position: {x: .0382860154, y: .221621141, z: -.017063085}
      rotation: {x: .156615227, y: .987296224, z: -.0141432397, w: -.022756584}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightArm
      position: {x: -.100501455, y: -2.49955224e-06, z: -5.15574072e-08}
      rotation: {x: .128958672, y: .988591135, z: -.0591317825, w: .0506032445}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightForeArm
      position: {x: .253428251, y: .00601135287, z: -.0167045239}
      rotation: {x: .173002318, y: .0184977558, z: -.0264102723, w: .984393537}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHand
      position: {x: .245373696, y: .0216417722, z: .00555046508}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex1
      position: {x: .0747694969, y: -.00124305359, z: .0343444981}
      rotation: {x: -.00423200894, y: .162119269, z: -.0406824313, w: .985923171}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex2
      position: {x: .0370584019, y: .00072612107, z: .0145388944}
      rotation: {x: -.0775835961, y: .0223498475, z: .040921364, w: .995894969}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex3
      position: {x: .0252250377, y: -.00496646529, z: .0110121462}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle1
      position: {x: .0756476447, y: .00479140272, z: .0118531818}
      rotation: {x: -.00177398103, y: .0143492613, z: -.047807496, w: .998751998}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle2
      position: {x: .0438090637, y: .000194188149, z: .00645493623}
      rotation: {x: -.0188719332, y: -.0441113934, z: .082947962, w: .995398283}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle3
      position: {x: .0330724716, y: -.00754753686, z: .00168984616}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky1
      position: {x: .0668033436, y: -.00199410878, z: -.0307561457}
      rotation: {x: -.0620294139, y: -.258612514, z: -.016704157, w: .963842809}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky2
      position: {x: .0285308417, y: -.001397143, z: -.0116237961}
      rotation: {x: .0298576318, y: .000797908462, z: -.0616652891, w: .997649908}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky3
      position: {x: .0214268602, y: -.000553508929, z: -.00851660781}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing1
      position: {x: .0705984756, y: .00245709647, z: -.00982145779}
      rotation: {x: -.0147603918, y: -.11599648, z: -.0297171939, w: .992695272}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing2
      position: {x: .0428871848, y: -.00137538207, z: -.00494585792}
      rotation: {x: .0207646266, y: -.0215577111, z: .0755871385, w: .996689916}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing3
      position: {x: .0295006037, y: -.00769293541, z: -.00462225592}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb1
      position: {x: .0146849155, y: -.0111049423, z: .0258580949}
      rotation: {x: -.119986929, y: .0336791351, z: .148827791, w: .980978668}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb2
      position: {x: .0163739994, y: -.00528999977, z: .0234913602}
      rotation: {x: -.0260633472, y: -.0966907069, z: -.00360694295, w: .994966686}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb3
      position: {x: .0254599992, y: -.00763999997, z: .0208330005}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    armTwist: .5
    foreArmTwist: .5
    upperLegTwist: .5
    legTwist: .5
    armStretch: .0500000007
    legStretch: .0500000007
    feetSpacing: 0
    rootMotionBoneName: 
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: e8914d097ece7cc48a83d5fccd4098c0,
    type: 3}
  animationType: 3
  additionalBone: 0
  userData: 
  assetBundleName: 
