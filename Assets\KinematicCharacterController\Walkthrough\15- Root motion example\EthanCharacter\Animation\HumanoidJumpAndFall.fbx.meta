fileFormatVersion: 2
guid: 51dd2e4c869794f75a0df7d54b210214
ModelImporter:
  serializedVersion: 18
  fileIDToRecycleName:
    100000: Chest
    100002: Geo_grp
    100004: Head
    100006: Hips
    100008: //RootNode
    100010: Jaw
    100012: <PERSON><PERSON><PERSON>D
    100014: <PERSON>_<PERSON>_Mesh
    100016: LeftArm
    100018: LeftCheek
    100020: LeftEye
    100022: LeftEyelidLower
    100024: LeftEyelidUpper
    100026: LeftFoot
    100028: LeftForeArm
    100030: LeftHand
    100032: LeftHandIndex1
    100034: LeftHandIndex2
    100036: LeftHandIndex3
    100038: LeftHandMiddle1
    100040: LeftHandMiddle2
    100042: LeftHandMiddle3
    100044: LeftHandPinky1
    100046: LeftHandPinky2
    100048: LeftHandPinky3
    100050: LeftHandRing1
    100052: LeftHandRing2
    100054: LeftHandRing3
    100056: LeftHandThumb1
    100058: LeftHandThumb2
    100060: LeftHandThumb3
    100062: Left<PERSON><PERSON><PERSON><PERSON>
    100064: LeftIOuterBrow
    100066: Left<PERSON>eg
    100068: <PERSON><PERSON>ipCorner
    100070: LeftLipLower
    100072: Left<PERSON>ipUpper
    100074: LeftNostril
    100076: LeftShoulder
    100078: LeftToes
    100080: LeftUpLeg
    100082: Lw_Teeth_Mesh
    100084: Neck
    100086: Reference
    100088: Ri_Eye_Mesh
    100090: RightArm
    100092: RightCheek
    100094: RightEye
    100096: RightEyelidLower
    100098: RightEyelidUpper
    100100: RightFoot
    100102: RightForeArm
    100104: RightHand
    100106: RightHandIndex1
    100108: RightHandIndex2
    100110: RightHandIndex3
    100112: RightHandMiddle1
    100114: RightHandMiddle2
    100116: RightHandMiddle3
    100118: RightHandPinky1
    100120: RightHandPinky2
    100122: RightHandPinky3
    100124: RightHandRing1
    100126: RightHandRing2
    100128: RightHandRing3
    100130: RightHandThumb1
    100132: RightHandThumb2
    100134: RightHandThumb3
    100136: RightInnerBrow
    100138: RightIOuterBrow
    100140: RightLeg
    100142: RightLipCorner
    100144: RightLipLower
    100146: RightLipUpper
    100148: RightNostril
    100150: RightShoulder
    100152: RightToes
    100154: RightUpLeg
    100156: Spine
    100158: TongueBack
    100160: TongueTip
    100162: Tounge_Mesh
    100164: Unity_Body_Mesh
    100166: Up_Teeth_Mesh
    400000: Chest
    400002: Geo_grp
    400004: Head
    400006: Hips
    400008: //RootNode
    400010: Jaw
    400012: JawEND
    400014: Le_Eye_Mesh
    400016: LeftArm
    400018: LeftCheek
    400020: LeftEye
    400022: LeftEyelidLower
    400024: LeftEyelidUpper
    400026: LeftFoot
    400028: LeftForeArm
    400030: LeftHand
    400032: LeftHandIndex1
    400034: LeftHandIndex2
    400036: LeftHandIndex3
    400038: LeftHandMiddle1
    400040: LeftHandMiddle2
    400042: LeftHandMiddle3
    400044: LeftHandPinky1
    400046: LeftHandPinky2
    400048: LeftHandPinky3
    400050: LeftHandRing1
    400052: LeftHandRing2
    400054: LeftHandRing3
    400056: LeftHandThumb1
    400058: LeftHandThumb2
    400060: LeftHandThumb3
    400062: LeftInnerBrow
    400064: LeftIOuterBrow
    400066: LeftLeg
    400068: LeftLipCorner
    400070: LeftLipLower
    400072: LeftLipUpper
    400074: LeftNostril
    400076: LeftShoulder
    400078: LeftToes
    400080: LeftUpLeg
    400082: Lw_Teeth_Mesh
    400084: Neck
    400086: Reference
    400088: Ri_Eye_Mesh
    400090: RightArm
    400092: RightCheek
    400094: RightEye
    400096: RightEyelidLower
    400098: RightEyelidUpper
    400100: RightFoot
    400102: RightForeArm
    400104: RightHand
    400106: RightHandIndex1
    400108: RightHandIndex2
    400110: RightHandIndex3
    400112: RightHandMiddle1
    400114: RightHandMiddle2
    400116: RightHandMiddle3
    400118: RightHandPinky1
    400120: RightHandPinky2
    400122: RightHandPinky3
    400124: RightHandRing1
    400126: RightHandRing2
    400128: RightHandRing3
    400130: RightHandThumb1
    400132: RightHandThumb2
    400134: RightHandThumb3
    400136: RightInnerBrow
    400138: RightIOuterBrow
    400140: RightLeg
    400142: RightLipCorner
    400144: RightLipLower
    400146: RightLipUpper
    400148: RightNostril
    400150: RightShoulder
    400152: RightToes
    400154: RightUpLeg
    400156: Spine
    400158: TongueBack
    400160: TongueTip
    400162: Tounge_Mesh
    400164: Unity_Body_Mesh
    400166: Up_Teeth_Mesh
    2300000: Le_Eye_Mesh
    2300002: Ri_Eye_Mesh
    3300000: Le_Eye_Mesh
    3300002: Ri_Eye_Mesh
    4300000: Le_Eye_Mesh
    4300002: Ri_Eye_Mesh
    4300004: Unity_Body_Mesh
    4300006: Up_Teeth_Mesh
    4300008: Lw_Teeth_Mesh
    4300010: Tounge_Mesh
    7400000: HumanoidJumpForwardLeft
    7400002: HumanoidFallLeft
    7400004: HumanoidJumpForwardRight
    7400006: HumanoidFallRight
    9500000: //RootNode
    13700000: Lw_Teeth_Mesh
    13700002: Tounge_Mesh
    13700004: Unity_Body_Mesh
    13700006: Up_Teeth_Mesh
  materials:
    importMaterials: 0
    materialName: 1
    materialSearch: 2
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    optimizeGameObjects: 0
    motionNodeName: 
    pivotNodeName: 
    animationCompression: 1
    animationRotationError: .5
    animationPositionError: .5
    animationScaleError: .5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: HumanoidJumpForwardLeft
      takeName: idle_jumpFwd_idle_tk01
      firstFrame: 350
      lastFrame: 352
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Geo_grp
        weight: 0
      - path: Geo_grp/Lw_Teeth_Mesh
        weight: 0
      - path: Geo_grp/Tounge_Mesh
        weight: 0
      - path: Geo_grp/Unity_Body_Mesh
        weight: 0
      - path: Geo_grp/Up_Teeth_Mesh
        weight: 0
      - path: Reference
        weight: 1
      - path: Reference/Hips
        weight: 1
      - path: Reference/Hips/LeftUpLeg
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg/LeftFoot
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes
        weight: 1
      - path: Reference/Hips/RightUpLeg
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg/RightFoot
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg/RightFoot/RightToes
        weight: 1
      - path: Reference/Hips/Spine
        weight: 1
      - path: Reference/Hips/Spine/Chest
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/JawEND
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipCorner
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/RightLipCorner
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/RightLipLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack/TongueTip
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftCheek
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEye
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEye/Le_Eye_Mesh
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEyelidLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEyelidUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftInnerBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftIOuterBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftLipUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftNostril
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightCheek
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEye
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEye/Ri_Eye_Mesh
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEyelidLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEyelidUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightInnerBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightIOuterBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightLipUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightNostril
        weight: 0
      - path: Reference/Hips/Spine/Chest/RightShoulder
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
    - serializedVersion: 16
      name: HumanoidFallLeft
      takeName: idle_jumpFwd_idle_tk01
      firstFrame: 355
      lastFrame: 357
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 1
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Geo_grp
        weight: 0
      - path: Geo_grp/Lw_Teeth_Mesh
        weight: 0
      - path: Geo_grp/Tounge_Mesh
        weight: 0
      - path: Geo_grp/Unity_Body_Mesh
        weight: 0
      - path: Geo_grp/Up_Teeth_Mesh
        weight: 0
      - path: Reference
        weight: 1
      - path: Reference/Hips
        weight: 1
      - path: Reference/Hips/LeftUpLeg
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg/LeftFoot
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes
        weight: 1
      - path: Reference/Hips/RightUpLeg
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg/RightFoot
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg/RightFoot/RightToes
        weight: 1
      - path: Reference/Hips/Spine
        weight: 1
      - path: Reference/Hips/Spine/Chest
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/JawEND
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipCorner
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/RightLipCorner
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/RightLipLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack/TongueTip
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftCheek
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEye
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEye/Le_Eye_Mesh
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEyelidLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEyelidUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftInnerBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftIOuterBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftLipUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftNostril
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightCheek
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEye
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEye/Ri_Eye_Mesh
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEyelidLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEyelidUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightInnerBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightIOuterBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightLipUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightNostril
        weight: 0
      - path: Reference/Hips/Spine/Chest/RightShoulder
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
    - serializedVersion: 16
      name: HumanoidJumpForwardRight
      takeName: idle_jumpFwd_idle_tk01
      firstFrame: 350
      lastFrame: 352
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 1
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Geo_grp
        weight: 0
      - path: Geo_grp/Lw_Teeth_Mesh
        weight: 0
      - path: Geo_grp/Tounge_Mesh
        weight: 0
      - path: Geo_grp/Unity_Body_Mesh
        weight: 0
      - path: Geo_grp/Up_Teeth_Mesh
        weight: 0
      - path: Reference
        weight: 1
      - path: Reference/Hips
        weight: 1
      - path: Reference/Hips/LeftUpLeg
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg/LeftFoot
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes
        weight: 1
      - path: Reference/Hips/RightUpLeg
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg/RightFoot
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg/RightFoot/RightToes
        weight: 1
      - path: Reference/Hips/Spine
        weight: 1
      - path: Reference/Hips/Spine/Chest
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/JawEND
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipCorner
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/RightLipCorner
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/RightLipLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack/TongueTip
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftCheek
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEye
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEye/Le_Eye_Mesh
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEyelidLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEyelidUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftInnerBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftIOuterBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftLipUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftNostril
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightCheek
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEye
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEye/Ri_Eye_Mesh
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEyelidLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEyelidUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightInnerBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightIOuterBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightLipUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightNostril
        weight: 0
      - path: Reference/Hips/Spine/Chest/RightShoulder
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
    - serializedVersion: 16
      name: HumanoidFallRight
      takeName: idle_jumpFwd_idle_tk01
      firstFrame: 355
      lastFrame: 357
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Geo_grp
        weight: 0
      - path: Geo_grp/Lw_Teeth_Mesh
        weight: 0
      - path: Geo_grp/Tounge_Mesh
        weight: 0
      - path: Geo_grp/Unity_Body_Mesh
        weight: 0
      - path: Geo_grp/Up_Teeth_Mesh
        weight: 0
      - path: Reference
        weight: 1
      - path: Reference/Hips
        weight: 1
      - path: Reference/Hips/LeftUpLeg
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg/LeftFoot
        weight: 1
      - path: Reference/Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes
        weight: 1
      - path: Reference/Hips/RightUpLeg
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg/RightFoot
        weight: 1
      - path: Reference/Hips/RightUpLeg/RightLeg/RightFoot/RightToes
        weight: 1
      - path: Reference/Hips/Spine
        weight: 1
      - path: Reference/Hips/Spine/Chest
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2
        weight: 1
      - path: Reference/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/JawEND
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipCorner
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/RightLipCorner
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/RightLipLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack/TongueTip
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftCheek
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEye
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEye/Le_Eye_Mesh
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEyelidLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftEyelidUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftInnerBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftIOuterBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftLipUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/LeftNostril
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightCheek
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEye
        weight: 1
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEye/Ri_Eye_Mesh
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEyelidLower
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightEyelidUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightInnerBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightIOuterBrow
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightLipUpper
        weight: 0
      - path: Reference/Hips/Spine/Chest/Neck/Head/RightNostril
        weight: 0
      - path: Reference/Hips/Spine/Chest/RightShoulder
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2
        weight: 1
      - path: Reference/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: .00999999978
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    splitTangentsAcrossUV: 1
    normalImportMode: 0
    tangentImportMode: 1
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToes
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToes
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftEye
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightEye
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Jaw
      humanName: Jaw
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: HumanoidJumpAndFall(Clone)
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Hips
      position: {x: -1.86264515e-08, y: .960032225, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftUpLeg
      position: {x: -.0754494965, y: -.0456640199, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLeg
      position: {x: -.0205504987, y: -.409129977, z: .00717136543}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftFoot
      position: {x: -.00515299942, y: -.423155904, z: -.0120320888}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftToes
      position: {x: -.00748699997, y: -.0731673017, z: .145427123}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightUpLeg
      position: {x: .0754495338, y: -.0456639901, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLeg
      position: {x: .0205504671, y: -.409130007, z: .00717136543}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightFoot
      position: {x: .00515299942, y: -.423155904, z: -.0120320888}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightToes
      position: {x: .00748699997, y: -.0731673017, z: .145427495}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Spine
      position: {x: -0, y: .0922631845, z: .0157713313}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Chest
      position: {x: -0, y: .162540287, z: .0218507219}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftShoulder
      position: {x: -.0382435061, y: .192178085, z: -.017063085}
      rotation: {x: -.00121677481, y: -.00917607173, z: .327255577, w: .944890559}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftArm
      position: {x: -.0835747719, y: .036097575, z: 0}
      rotation: {x: .208304122, y: -.055566486, z: -.310117185, w: .925931454}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftForeArm
      position: {x: -.254049301, y: 0, z: 0}
      rotation: {x: .00065340061, y: .0216908138, z: -.0116735483, w: .999696374}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHand
      position: {x: -.24638927, y: 0, z: 0}
      rotation: {x: .00174571283, y: -.0316711254, z: .0242843106, w: .999201775}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex1
      position: {x: -.0751257986, y: -.00784140453, z: .0326526426}
      rotation: {x: -.00211892044, y: .0802574381, z: .0175381862, w: .996617615}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex2
      position: {x: -.03979728, y: 4.98084046e-05, z: .00118575036}
      rotation: {x: .000501934439, y: .0154705783, z: .0404105186, w: .999063313}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex3
      position: {x: -.0279684775, y: -6.28122399e-09, z: -5.17186614e-08}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle1
      position: {x: -.0760238245, y: -.00188513438, z: .0101412293}
      rotation: {x: -.000768873433, y: .0333210826, z: .020907538, w: .999225736}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle2
      position: {x: -.0442804359, y: 4.79887422e-06, z: -.000425400125}
      rotation: {x: -.00136209256, y: -.0191520527, z: .0378838927, w: .999097645}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle3
      position: {x: -.0339648277, y: -1.21979289e-08, z: 3.75648268e-09}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky1
      position: {x: -.0656599477, y: -.00782510638, z: -.0322512463}
      rotation: {x: -.000912672316, y: .0121611441, z: .0212233849, w: .999700367}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky2
      position: {x: -.0308054481, y: -3.0874573e-05, z: -.0014480775}
      rotation: {x: -.000226802338, y: -.00969417952, z: .000434517511, w: .999952912}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky3
      position: {x: -.0230640266, y: -6.40258077e-06, z: 1.8332095e-08}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing1
      position: {x: -.0703021064, y: -.00374530931, z: -.0114117917}
      rotation: {x: -.000324145716, y: .0115975412, z: .0247372258, w: .999626696}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing2
      position: {x: -.0431354567, y: -2.08823076e-05, z: -.00223517837}
      rotation: {x: -.00120344944, y: -.0231133532, z: .0409869365, w: .998891592}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing3
      position: {x: -.0308355652, y: 7.71049682e-11, z: -1.64932707e-08}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb1
      position: {x: -.0142312413, y: -.0123778246, z: .0255316682}
      rotation: {x: -.1395832, y: -.0351250619, z: -.0833107978, w: .98607409}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb2
      position: {x: -.0163739994, y: -.00528999977, z: .0234914087}
      rotation: {x: -.026061492, y: .0966902077, z: .00360864005, w: .994966805}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb3
      position: {x: -.0254599992, y: -.00763999997, z: .0208330005}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Neck
      position: {x: -0, y: .235723898, z: -.0324132554}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Head
      position: {x: -0, y: .106355801, z: .0113267815}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Jaw
      position: {x: -0, y: .0111267585, z: .0103275431}
      rotation: {x: .219240054, y: -0, z: -0, w: .975670993}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: JawEND
      position: {x: -0, y: -.0482887588, z: .071851708}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipCorner
      position: {x: -.032843262, y: -.01657876, z: .0661217645}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipLower
      position: {x: -.0142508168, y: -.0216887593, z: .0822406337}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipCorner
      position: {x: .0328399986, y: -.01657876, z: .0661187842}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipLower
      position: {x: .0142508168, y: -.0216887593, z: .0822387859}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: TongueBack
      position: {x: -0, y: -.022869369, z: .0100954091}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: TongueTip
      position: {x: -0, y: -.000409444125, z: .0282272995}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftCheek
      position: {x: -.0542440265, y: .0337019488, z: .0594304018}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEye
      position: {x: -.0208482333, y: .0825027004, z: .0554274321}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEyelidLower
      position: {x: -.0356189571, y: .0650736615, z: .076234743}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEyelidUpper
      position: {x: -.0344068967, y: .10060814, z: .0802053064}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftInnerBrow
      position: {x: -.0120626912, y: .118765265, z: .0934668258}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftIOuterBrow
      position: {x: -.0550398715, y: .114825293, z: .061777398}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipUpper
      position: {x: -.0145013221, y: -.00511181122, z: .094618842}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftNostril
      position: {x: -.0178999994, y: .0263128281, z: .0908674002}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightCheek
      position: {x: .0542399958, y: .033702828, z: .0594273992}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEye
      position: {x: .020849999, y: .082502827, z: .0554273985}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEyelidLower
      position: {x: .0356200002, y: .065072827, z: .0762374029}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEyelidUpper
      position: {x: .0344099998, y: .100612827, z: .0802073926}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightInnerBrow
      position: {x: .0120626874, y: .118765265, z: .0934668258}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightIOuterBrow
      position: {x: .0550400019, y: .114822827, z: .061777398}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipUpper
      position: {x: .0145013221, y: -.00510717137, z: .094617404}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightNostril
      position: {x: .0178999994, y: .0263089053, z: .0908706188}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightShoulder
      position: {x: .0383285098, y: .192176744, z: -.017063085}
      rotation: {x: .290413886, y: .956010222, z: .0322408788, w: -.02578477}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightArm
      position: {x: -.0835755169, y: .0360957012, z: -5.15574072e-08}
      rotation: {x: .263129145, y: .953624487, z: -.146085501, w: .00475239567}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightForeArm
      position: {x: .253428251, y: .00601135287, z: -.0167045239}
      rotation: {x: -.0153278718, y: .0147472881, z: -.0201126598, w: .999571443}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHand
      position: {x: .245373696, y: .0216417722, z: .00555046508}
      rotation: {x: .000613642507, y: .036897447, z: -.0569219254, w: .9976964}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex1
      position: {x: .0747694969, y: -.00124305359, z: .0343444981}
      rotation: {x: -.00211892044, y: .0802574381, z: .0175381862, w: .996617615}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex2
      position: {x: .0370584019, y: .00072612107, z: .0145388944}
      rotation: {x: -.00332621601, y: .0159315318, z: .060632892, w: .998027444}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex3
      position: {x: .0252250377, y: -.00496646529, z: .0110121462}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle1
      position: {x: .0756476447, y: .00479140272, z: .0118531818}
      rotation: {x: -.000768873433, y: .0333210826, z: .020907538, w: .999225736}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle2
      position: {x: .0438090637, y: .000194188149, z: .00645493623}
      rotation: {x: -.00413046591, y: -.0335116945, z: .0761208013, w: .996526778}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle3
      position: {x: .0330724716, y: -.00754753686, z: .00168984616}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky1
      position: {x: .0668033436, y: -.00199410878, z: -.0307561457}
      rotation: {x: .0031761073, y: -.192005888, z: .0451137684, w: .98035115}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky2
      position: {x: .0285308417, y: -.001397143, z: -.0116237961}
      rotation: {x: -.00225326815, y: -.00959618483, z: -.0107575748, w: .999893546}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky3
      position: {x: .0214268602, y: -.000553508929, z: -.00851660781}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing1
      position: {x: .0705984756, y: .00245709647, z: -.00982145779}
      rotation: {x: .000710848777, y: -.0543420911, z: .0349445045, w: .997910559}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing2
      position: {x: .0428871848, y: -.00137538207, z: -.00494585792}
      rotation: {x: .000484766497, y: -.0212895721, z: .0698628947, w: .997329295}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing3
      position: {x: .0295006037, y: -.00769293541, z: -.00462225592}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb1
      position: {x: .0146849155, y: -.0111049423, z: .0258580949}
      rotation: {x: -.108707562, y: .00948966853, z: .100395039, w: .988945663}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb2
      position: {x: .0163739994, y: -.00528999977, z: .0234913602}
      rotation: {x: -.0260640942, y: -.0966886356, z: -.00360555435, w: .994966805}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb3
      position: {x: .0254599992, y: -.00763999997, z: .0208330005}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    armTwist: .5
    foreArmTwist: .5
    upperLegTwist: .5
    legTwist: .5
    armStretch: .0500000007
    legStretch: .0500000007
    feetSpacing: 0
    rootMotionBoneName: 
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 9bde93922c5ea4196b87f9b5593da1dc,
    type: 3}
  animationType: 3
  additionalBone: 0
  userData: 
  assetBundleName: 
