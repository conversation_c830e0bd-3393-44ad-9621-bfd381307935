fileFormatVersion: 2
guid: b1a5e04ae51004842aba06704a6c2903
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Chest
    100002: chestProxy_geo
    100004: //RootNode
    100006: Head
    100008: headProxy_geo
    100010: HeadTop_End
    100012: Hips
    100014: Jaw
    100016: JawEND
    100018: jawProxy_geo
    100020: l_ankleProxy_geo
    100022: l_ballProxy_geo
    100024: l_clavicleProxy_geo
    100026: l_erbowProxy_geo
    100028: l_hipProxy_geo
    100030: l_indexProxy_01_geo
    100032: l_indexProxy_02_geo
    100034: l_indexProxy_03_geo
    100036: l_kneeProxy_geo
    100038: l_middleProxy_01_geo
    100040: l_middleProxy_02_geo
    100042: l_middleProxy_03_geo
    100044: l_pinkyProxy_01_geo
    100046: l_pinkyProxy_02_geo
    100048: l_pinkyProxy_03_geo
    100050: l_ringProxy_01_geo
    100052: l_ringProxy_02_geo
    100054: l_ringProxy_03_geo
    100056: l_shourderProxy_geo
    100058: l_thumbProxy_01_geo
    100060: l_thumbProxy_02_geo
    100062: l_thumbProxy_03_geo
    100064: l_UNI_eye
    100066: l_wristProxy_geo
    100068: LeftArm
    100070: LeftCheek
    100072: LeftEye
    100074: LeftEyelidLower
    100076: LeftEyelidUpper
    100078: LeftFoot
    100080: LeftForeArm
    100082: LeftHand
    100084: LeftHandIndex1
    100086: LeftHandIndex13
    100088: LeftHandIndex17
    100090: LeftHandIndex2
    100092: LeftHandIndex3
    100094: LeftHandMiddle1
    100096: LeftHandMiddle13
    100098: LeftHandMiddle17
    100100: LeftHandMiddle2
    100102: LeftHandMiddle3
    100104: LeftHandPinky1
    100106: LeftHandPinky13
    100108: LeftHandPinky17
    100110: LeftHandPinky2
    100112: LeftHandPinky3
    100114: LeftHandRing1
    100116: LeftHandRing13
    100118: LeftHandRing17
    100120: LeftHandRing2
    100122: LeftHandRing3
    100124: LeftHandThumb1
    100126: LeftHandThumb13
    100128: LeftHandThumb17
    100130: LeftHandThumb2
    100132: LeftHandThumb3
    100134: LeftInnerBrow
    100136: LeftIOuterBrow
    100138: LeftLeg
    100140: LeftLipCorner
    100142: LeftLipLower
    100144: LeftLipUpper
    100146: LeftNostril
    100148: LeftShoulder
    100150: LeftToes
    100152: LeftUpLeg
    100154: LToeBase_End2
    100156: LToeBase_End3
    100158: Neck
    100160: neckProxy_geo
    100162: pelvisProxy_geo
    100164: r_ankleProxy_geo
    100166: r_ballProxy_geo
    100168: r_clavicleProxy_geo
    100170: r_erbowProxy_geo
    100172: r_hipProxy_geo
    100174: r_indexProxy_01_geo
    100176: r_indexProxy_02_geo
    100178: r_indexProxy_03_geo
    100180: r_kneeProxy_geo
    100182: r_middleProxy_01_geo
    100184: r_middleProxy_02_geo
    100186: r_middleProxy_03_geo
    100188: r_pinkyProxy_01_geo
    100190: r_pinkyProxy_02_geo
    100192: r_pinkyProxy_03_geo
    100194: r_ringProxy_01_geo
    100196: r_ringProxy_02_geo
    100198: r_ringProxy_03_geo
    100200: r_shourderProxy_geo
    100202: r_thumbProxy_01_geo
    100204: r_thumbProxy_02_geo
    100206: r_thumbProxy_03_geo
    100208: r_UNI_eye
    100210: r_wristProxy_geo
    100212: Reference
    100214: RightArm
    100216: RightCheek
    100218: RightEye
    100220: RightEyelidLower
    100222: RightEyelidUpper
    100224: RightFoot
    100226: RightForeArm
    100228: RightHand
    100230: RightHandIndex1
    100232: RightHandIndex2
    100234: RightHandIndex3
    100236: RightHandMiddle1
    100238: RightHandMiddle2
    100240: RightHandMiddle3
    100242: RightHandPinky1
    100244: RightHandPinky2
    100246: RightHandPinky3
    100248: RightHandRing1
    100250: RightHandRing2
    100252: RightHandRing3
    100254: RightHandThumb1
    100256: RightHandThumb2
    100258: RightHandThumb3
    100260: RightInnerBrow
    100262: RightIOuterBrow
    100264: RightLeg
    100266: RightLipCorner
    100268: RightLipLower
    100270: RightLipUpper
    100272: RightNostril
    100274: RightShoulder
    100276: RightToes
    100278: RightUpLeg
    100280: Spine
    100282: spineProxy_geo
    100284: TongueBack
    100286: TongueTip
    100288: UNI_01_Lower_teethProxy
    100290: UNI_01_TongueBaseProxy
    100292: UNI_01_TongueTipProxy
    100294: UNI_01_Upper_teethProxy
    400000: Chest
    400002: chestProxy_geo
    400004: //RootNode
    400006: Head
    400008: headProxy_geo
    400010: HeadTop_End
    400012: Hips
    400014: Jaw
    400016: JawEND
    400018: jawProxy_geo
    400020: l_ankleProxy_geo
    400022: l_ballProxy_geo
    400024: l_clavicleProxy_geo
    400026: l_erbowProxy_geo
    400028: l_hipProxy_geo
    400030: l_indexProxy_01_geo
    400032: l_indexProxy_02_geo
    400034: l_indexProxy_03_geo
    400036: l_kneeProxy_geo
    400038: l_middleProxy_01_geo
    400040: l_middleProxy_02_geo
    400042: l_middleProxy_03_geo
    400044: l_pinkyProxy_01_geo
    400046: l_pinkyProxy_02_geo
    400048: l_pinkyProxy_03_geo
    400050: l_ringProxy_01_geo
    400052: l_ringProxy_02_geo
    400054: l_ringProxy_03_geo
    400056: l_shourderProxy_geo
    400058: l_thumbProxy_01_geo
    400060: l_thumbProxy_02_geo
    400062: l_thumbProxy_03_geo
    400064: l_UNI_eye
    400066: l_wristProxy_geo
    400068: LeftArm
    400070: LeftCheek
    400072: LeftEye
    400074: LeftEyelidLower
    400076: LeftEyelidUpper
    400078: LeftFoot
    400080: LeftForeArm
    400082: LeftHand
    400084: LeftHandIndex1
    400086: LeftHandIndex13
    400088: LeftHandIndex17
    400090: LeftHandIndex2
    400092: LeftHandIndex3
    400094: LeftHandMiddle1
    400096: LeftHandMiddle13
    400098: LeftHandMiddle17
    400100: LeftHandMiddle2
    400102: LeftHandMiddle3
    400104: LeftHandPinky1
    400106: LeftHandPinky13
    400108: LeftHandPinky17
    400110: LeftHandPinky2
    400112: LeftHandPinky3
    400114: LeftHandRing1
    400116: LeftHandRing13
    400118: LeftHandRing17
    400120: LeftHandRing2
    400122: LeftHandRing3
    400124: LeftHandThumb1
    400126: LeftHandThumb13
    400128: LeftHandThumb17
    400130: LeftHandThumb2
    400132: LeftHandThumb3
    400134: LeftInnerBrow
    400136: LeftIOuterBrow
    400138: LeftLeg
    400140: LeftLipCorner
    400142: LeftLipLower
    400144: LeftLipUpper
    400146: LeftNostril
    400148: LeftShoulder
    400150: LeftToes
    400152: LeftUpLeg
    400154: LToeBase_End2
    400156: LToeBase_End3
    400158: Neck
    400160: neckProxy_geo
    400162: pelvisProxy_geo
    400164: r_ankleProxy_geo
    400166: r_ballProxy_geo
    400168: r_clavicleProxy_geo
    400170: r_erbowProxy_geo
    400172: r_hipProxy_geo
    400174: r_indexProxy_01_geo
    400176: r_indexProxy_02_geo
    400178: r_indexProxy_03_geo
    400180: r_kneeProxy_geo
    400182: r_middleProxy_01_geo
    400184: r_middleProxy_02_geo
    400186: r_middleProxy_03_geo
    400188: r_pinkyProxy_01_geo
    400190: r_pinkyProxy_02_geo
    400192: r_pinkyProxy_03_geo
    400194: r_ringProxy_01_geo
    400196: r_ringProxy_02_geo
    400198: r_ringProxy_03_geo
    400200: r_shourderProxy_geo
    400202: r_thumbProxy_01_geo
    400204: r_thumbProxy_02_geo
    400206: r_thumbProxy_03_geo
    400208: r_UNI_eye
    400210: r_wristProxy_geo
    400212: Reference
    400214: RightArm
    400216: RightCheek
    400218: RightEye
    400220: RightEyelidLower
    400222: RightEyelidUpper
    400224: RightFoot
    400226: RightForeArm
    400228: RightHand
    400230: RightHandIndex1
    400232: RightHandIndex2
    400234: RightHandIndex3
    400236: RightHandMiddle1
    400238: RightHandMiddle2
    400240: RightHandMiddle3
    400242: RightHandPinky1
    400244: RightHandPinky2
    400246: RightHandPinky3
    400248: RightHandRing1
    400250: RightHandRing2
    400252: RightHandRing3
    400254: RightHandThumb1
    400256: RightHandThumb2
    400258: RightHandThumb3
    400260: RightInnerBrow
    400262: RightIOuterBrow
    400264: RightLeg
    400266: RightLipCorner
    400268: RightLipLower
    400270: RightLipUpper
    400272: RightNostril
    400274: RightShoulder
    400276: RightToes
    400278: RightUpLeg
    400280: Spine
    400282: spineProxy_geo
    400284: TongueBack
    400286: TongueTip
    400288: UNI_01_Lower_teethProxy
    400290: UNI_01_TongueBaseProxy
    400292: UNI_01_TongueTipProxy
    400294: UNI_01_Upper_teethProxy
    2300000: chestProxy_geo
    2300002: headProxy_geo
    2300004: jawProxy_geo
    2300006: l_ankleProxy_geo
    2300008: l_ballProxy_geo
    2300010: l_clavicleProxy_geo
    2300012: l_erbowProxy_geo
    2300014: l_hipProxy_geo
    2300016: l_indexProxy_01_geo
    2300018: l_indexProxy_02_geo
    2300020: l_indexProxy_03_geo
    2300022: l_kneeProxy_geo
    2300024: l_middleProxy_01_geo
    2300026: l_middleProxy_02_geo
    2300028: l_middleProxy_03_geo
    2300030: l_pinkyProxy_01_geo
    2300032: l_pinkyProxy_02_geo
    2300034: l_pinkyProxy_03_geo
    2300036: l_ringProxy_01_geo
    2300038: l_ringProxy_02_geo
    2300040: l_ringProxy_03_geo
    2300042: l_shourderProxy_geo
    2300044: l_thumbProxy_01_geo
    2300046: l_thumbProxy_02_geo
    2300048: l_thumbProxy_03_geo
    2300050: l_UNI_eye
    2300052: l_wristProxy_geo
    2300054: neckProxy_geo
    2300056: pelvisProxy_geo
    2300058: r_ankleProxy_geo
    2300060: r_ballProxy_geo
    2300062: r_clavicleProxy_geo
    2300064: r_erbowProxy_geo
    2300066: r_hipProxy_geo
    2300068: r_indexProxy_01_geo
    2300070: r_indexProxy_02_geo
    2300072: r_indexProxy_03_geo
    2300074: r_kneeProxy_geo
    2300076: r_middleProxy_01_geo
    2300078: r_middleProxy_02_geo
    2300080: r_middleProxy_03_geo
    2300082: r_pinkyProxy_01_geo
    2300084: r_pinkyProxy_02_geo
    2300086: r_pinkyProxy_03_geo
    2300088: r_ringProxy_01_geo
    2300090: r_ringProxy_02_geo
    2300092: r_ringProxy_03_geo
    2300094: r_shourderProxy_geo
    2300096: r_thumbProxy_01_geo
    2300098: r_thumbProxy_02_geo
    2300100: r_thumbProxy_03_geo
    2300102: r_UNI_eye
    2300104: r_wristProxy_geo
    2300106: spineProxy_geo
    2300108: UNI_01_Lower_teethProxy
    2300110: UNI_01_TongueBaseProxy
    2300112: UNI_01_TongueTipProxy
    2300114: UNI_01_Upper_teethProxy
    3300000: chestProxy_geo
    3300002: headProxy_geo
    3300004: jawProxy_geo
    3300006: l_ankleProxy_geo
    3300008: l_ballProxy_geo
    3300010: l_clavicleProxy_geo
    3300012: l_erbowProxy_geo
    3300014: l_hipProxy_geo
    3300016: l_indexProxy_01_geo
    3300018: l_indexProxy_02_geo
    3300020: l_indexProxy_03_geo
    3300022: l_kneeProxy_geo
    3300024: l_middleProxy_01_geo
    3300026: l_middleProxy_02_geo
    3300028: l_middleProxy_03_geo
    3300030: l_pinkyProxy_01_geo
    3300032: l_pinkyProxy_02_geo
    3300034: l_pinkyProxy_03_geo
    3300036: l_ringProxy_01_geo
    3300038: l_ringProxy_02_geo
    3300040: l_ringProxy_03_geo
    3300042: l_shourderProxy_geo
    3300044: l_thumbProxy_01_geo
    3300046: l_thumbProxy_02_geo
    3300048: l_thumbProxy_03_geo
    3300050: l_UNI_eye
    3300052: l_wristProxy_geo
    3300054: neckProxy_geo
    3300056: pelvisProxy_geo
    3300058: r_ankleProxy_geo
    3300060: r_ballProxy_geo
    3300062: r_clavicleProxy_geo
    3300064: r_erbowProxy_geo
    3300066: r_hipProxy_geo
    3300068: r_indexProxy_01_geo
    3300070: r_indexProxy_02_geo
    3300072: r_indexProxy_03_geo
    3300074: r_kneeProxy_geo
    3300076: r_middleProxy_01_geo
    3300078: r_middleProxy_02_geo
    3300080: r_middleProxy_03_geo
    3300082: r_pinkyProxy_01_geo
    3300084: r_pinkyProxy_02_geo
    3300086: r_pinkyProxy_03_geo
    3300088: r_ringProxy_01_geo
    3300090: r_ringProxy_02_geo
    3300092: r_ringProxy_03_geo
    3300094: r_shourderProxy_geo
    3300096: r_thumbProxy_01_geo
    3300098: r_thumbProxy_02_geo
    3300100: r_thumbProxy_03_geo
    3300102: r_UNI_eye
    3300104: r_wristProxy_geo
    3300106: spineProxy_geo
    3300108: UNI_01_Lower_teethProxy
    3300110: UNI_01_TongueBaseProxy
    3300112: UNI_01_TongueTipProxy
    3300114: UNI_01_Upper_teethProxy
    4300000: l_UNI_eye
    4300002: r_UNI_eye
    4300004: UNI_01_TongueBaseProxy
    4300006: UNI_01_TongueTipProxy
    4300008: UNI_01_Lower_teethProxy
    4300010: jawProxy_geo
    4300012: headProxy_geo
    4300014: UNI_01_Upper_teethProxy
    4300016: neckProxy_geo
    4300018: r_pinkyProxy_03_geo
    4300020: r_pinkyProxy_02_geo
    4300022: r_pinkyProxy_01_geo
    4300024: r_ringProxy_03_geo
    4300026: r_ringProxy_02_geo
    4300028: r_ringProxy_01_geo
    4300030: r_middleProxy_03_geo
    4300032: r_middleProxy_02_geo
    4300034: r_middleProxy_01_geo
    4300036: r_indexProxy_03_geo
    4300038: r_indexProxy_02_geo
    4300040: r_indexProxy_01_geo
    4300042: r_thumbProxy_03_geo
    4300044: r_thumbProxy_02_geo
    4300046: r_thumbProxy_01_geo
    4300048: r_wristProxy_geo
    4300050: r_erbowProxy_geo
    4300052: r_shourderProxy_geo
    4300054: r_clavicleProxy_geo
    4300056: chestProxy_geo
    4300058: l_pinkyProxy_03_geo
    4300060: l_pinkyProxy_02_geo
    4300062: l_pinkyProxy_01_geo
    4300064: l_ringProxy_03_geo
    4300066: l_ringProxy_02_geo
    4300068: l_ringProxy_01_geo
    4300070: l_middleProxy_03_geo
    4300072: l_middleProxy_02_geo
    4300074: l_middleProxy_01_geo
    4300076: l_indexProxy_03_geo
    4300078: l_indexProxy_02_geo
    4300080: l_indexProxy_01_geo
    4300082: l_thumbProxy_03_geo
    4300084: l_thumbProxy_02_geo
    4300086: l_thumbProxy_01_geo
    4300088: l_wristProxy_geo
    4300090: l_erbowProxy_geo
    4300092: l_shourderProxy_geo
    4300094: l_clavicleProxy_geo
    4300096: spineProxy_geo
    4300098: r_ballProxy_geo
    4300100: r_ankleProxy_geo
    4300102: r_kneeProxy_geo
    4300104: r_hipProxy_geo
    4300106: pelvisProxy_geo
    4300108: l_ballProxy_geo
    4300110: l_ankleProxy_geo
    4300112: l_kneeProxy_geo
    4300114: l_hipProxy_geo
    7400000: HumanoidWalk
    9500000: //RootNode
  materials:
    importMaterials: 0
    materialName: 1
    materialSearch: 2
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleRotations: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 0
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: HumanoidWalk
      takeName: _1_a_U1_M_P_WalkForward_NtrlFaceFwd__Fb_p0_No_0_PJ_3
      firstFrame: 215.2
      lastFrame: 244.9
      wrapMode: 0
      orientationOffsetY: 2.88
      level: 0
      cycleOffset: -0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.01
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToes
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToes
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftEye
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightEye
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Jaw
      humanName: Jaw
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: HumanoidWalk(Clone)
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Hips
      position: {x: -0.000000016763806, y: 0.9555335, z: 0.07758622}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftUpLeg
      position: {x: -0.0754495, y: -0.04566402, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLeg
      position: {x: -0.020550499, y: -0.40912998, z: -0.00071864796}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftFoot
      position: {x: -0.0051529994, y: -0.4231559, z: -0.027648851}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftToes
      position: {x: -0.007487, y: -0.0731673, z: 0.14542712}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightUpLeg
      position: {x: 0.075449534, y: -0.04566399, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLeg
      position: {x: 0.020550467, y: -0.40913, z: -0.00071864796}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightFoot
      position: {x: 0.0051529994, y: -0.4231559, z: -0.027648851}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightToes
      position: {x: 0.007487, y: -0.0731673, z: 0.1454275}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Spine
      position: {x: 2.646978e-25, y: 0.092263184, z: 0.015771331}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Chest
      position: {x: -0, y: 0.16254029, z: -0.0016560555}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftShoulder
      position: {x: -0.038285997, y: 0.2216225, z: -0.017063085}
      rotation: {x: -0.030223321, y: -0.07990193, z: 0.14446756, w: 0.9858151}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftArm
      position: {x: -0.10050205, y: 5.684342e-16, z: -3.330669e-18}
      rotation: {x: 0.008133877, y: 0.0757869, z: -0.1321358, w: 0.98829675}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftForeArm
      position: {x: -0.2540493, y: 5.684342e-16, z: 1.11022296e-17}
      rotation: {x: 0.2781269, y: 0.03635174, z: -0.015607543, w: 0.9597293}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHand
      position: {x: -0.24638927, y: 0, z: -1.9984014e-16}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex1
      position: {x: -0.0751258, y: -0.0078414045, z: 0.032652643}
      rotation: {x: 0.06495643, y: 0.05091051, z: 0.058088716, w: 0.9948942}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex2
      position: {x: -0.03979728, y: 0.000049808405, z: 0.0011857504}
      rotation: {x: -0.06737132, y: 0.015346782, z: 0.033307686, w: 0.9970538}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandIndex3
      position: {x: -0.027968477, y: -0.000000006281225, z: -0.00000005171866}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle1
      position: {x: -0.076023825, y: -0.0018851344, z: 0.010141229}
      rotation: {x: -0.09939486, y: 0.04107085, z: 0.09351314, w: 0.9897925}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle2
      position: {x: -0.044280436, y: 0.000004798874, z: -0.00042540013}
      rotation: {x: -0.012435689, y: -0.0076595433, z: 0.031807605, w: 0.9993873}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandMiddle3
      position: {x: -0.033964828, y: -0.000000012197929, z: 0.0000000037564827}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky1
      position: {x: -0.06565995, y: -0.007825106, z: -0.032251246}
      rotation: {x: -0.28387696, y: 0.036568172, z: 0.087664604, w: 0.95414436}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky2
      position: {x: -0.030805448, y: -0.000030874573, z: -0.0014480775}
      rotation: {x: 0.047048137, y: -0.021200087, z: 0.037495792, w: 0.9979635}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandPinky3
      position: {x: -0.023064027, y: -0.0000064025808, z: 0.000000018332095}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing1
      position: {x: -0.07030211, y: -0.0037453093, z: -0.011411792}
      rotation: {x: -0.10562233, y: 0.056129266, z: 0.08703209, w: 0.988999}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing2
      position: {x: -0.043135457, y: -0.000020882308, z: -0.0022351784}
      rotation: {x: 0.018426064, y: -0.02561071, z: 0.033295766, w: 0.99894744}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandRing3
      position: {x: -0.030835565, y: 7.7103546e-11, z: -0.00000001649327}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb1
      position: {x: -0.014231241, y: -0.012377825, z: 0.025531668}
      rotation: {x: -0.14225604, y: -0.055378057, z: -0.12830889, w: 0.979915}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb2
      position: {x: -0.016374, y: -0.00529, z: 0.023491409}
      rotation: {x: -0.02606398, y: 0.096689634, z: 0.003605904, w: 0.9949668}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftHandThumb3
      position: {x: -0.02546, y: -0.00764, z: 0.020833}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Neck
      position: {x: -0, y: 0.2590093, z: -0.032413255}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Head
      position: {x: -2.646978e-25, y: 0.08307038, z: 0.0113267815}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: Jaw
      position: {x: 1.7347234e-20, y: 0.0111267585, z: 0.010327543}
      rotation: {x: 0.21924005, y: -0, z: -0, w: 0.975671}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: JawEND
      position: {x: -1.7347234e-20, y: -0.04828876, z: 0.07185171}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipCorner
      position: {x: -0.032843262, y: -0.01657876, z: 0.066121764}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipLower
      position: {x: -0.014250817, y: -0.02168876, z: 0.08224063}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipCorner
      position: {x: 0.03284, y: -0.01657876, z: 0.066118784}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipLower
      position: {x: 0.014250817, y: -0.02168876, z: 0.082238786}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: TongueBack
      position: {x: -1.7347234e-20, y: -0.022869369, z: 0.010095409}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: TongueTip
      position: {x: -1.7347234e-20, y: -0.023278812, z: 0.03832271}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftCheek
      position: {x: -0.054244027, y: 0.03370195, z: 0.0594304}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEye
      position: {x: -0.020848233, y: 0.0825027, z: 0.055427432}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEyelidLower
      position: {x: -0.035618957, y: 0.06507366, z: 0.07623474}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftEyelidUpper
      position: {x: -0.034406897, y: 0.10060814, z: 0.08020531}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftInnerBrow
      position: {x: -0.012062691, y: 0.118765265, z: 0.093466826}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftIOuterBrow
      position: {x: -0.05503987, y: 0.11482529, z: 0.061777398}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftLipUpper
      position: {x: -0.014501322, y: -0.005111811, z: 0.09461884}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: LeftNostril
      position: {x: -0.0179, y: 0.026312828, z: 0.0908674}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightCheek
      position: {x: 0.054239996, y: 0.033702828, z: 0.0594274}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEye
      position: {x: 0.020849999, y: 0.08250283, z: 0.0554274}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEyelidLower
      position: {x: 0.03562, y: 0.06507283, z: 0.0762374}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightEyelidUpper
      position: {x: 0.03441, y: 0.10061283, z: 0.08020739}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightInnerBrow
      position: {x: 0.012062687, y: 0.118765265, z: 0.093466826}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightIOuterBrow
      position: {x: 0.055040002, y: 0.11482283, z: 0.061777398}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightLipUpper
      position: {x: 0.014501322, y: -0.0051071714, z: 0.094617404}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightNostril
      position: {x: 0.0179, y: 0.026308905, z: 0.09087062}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightShoulder
      position: {x: 0.038286015, y: 0.22162114, z: -0.017063085}
      rotation: {x: 0.15105928, y: 0.98671633, z: -0.021163033, w: -0.055894263}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightArm
      position: {x: -0.100501455, y: -0.0000024995522, z: -0.000000051557407}
      rotation: {x: 0.12567478, y: 0.98442847, z: 0.062543266, w: 0.105805375}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightForeArm
      position: {x: 0.25342825, y: 0.006011353, z: -0.016704524}
      rotation: {x: 0.26110226, y: 0.01888748, z: -0.030674139, w: 0.96463877}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHand
      position: {x: 0.2453737, y: 0.021641772, z: 0.005550465}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex1
      position: {x: 0.0747695, y: -0.0012430536, z: 0.034344498}
      rotation: {x: 0.06961239, y: 0.11270627, z: -0.010377135, w: 0.99113256}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex2
      position: {x: 0.0370584, y: 0.00072612107, z: 0.014538894}
      rotation: {x: -0.068798974, y: 0.021605203, z: 0.042188462, w: 0.99650395}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandIndex3
      position: {x: 0.025225038, y: -0.0049664653, z: 0.011012146}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle1
      position: {x: 0.075647645, y: 0.0047914027, z: 0.011853182}
      rotation: {x: -0.092045546, y: 0.020684198, z: -0.060492296, w: 0.9937004}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle2
      position: {x: 0.043809064, y: 0.00019418815, z: 0.006454936}
      rotation: {x: -0.016959926, y: -0.04367115, z: 0.0809238, w: 0.99561864}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandMiddle3
      position: {x: 0.03307247, y: -0.007547537, z: 0.0016898462}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky1
      position: {x: 0.06680334, y: -0.0019941088, z: -0.030756146}
      rotation: {x: -0.28647894, y: -0.21416986, z: 0.021083327, w: 0.9336042}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky2
      position: {x: 0.028530842, y: -0.001397143, z: -0.011623796}
      rotation: {x: 0.029356524, y: 0.0005622971, z: -0.062125385, w: 0.99763644}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandPinky3
      position: {x: 0.02142686, y: -0.00055350893, z: -0.008516608}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing1
      position: {x: 0.070598476, y: 0.0024570965, z: -0.009821458}
      rotation: {x: -0.10453735, y: -0.1014307, z: -0.025803583, w: 0.9889985}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing2
      position: {x: 0.042887185, y: -0.0013753821, z: -0.004945858}
      rotation: {x: 0.020977763, y: -0.021642664, z: 0.07535203, w: 0.9967014}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandRing3
      position: {x: 0.029500604, y: -0.0076929354, z: -0.004622256}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb1
      position: {x: 0.014684916, y: -0.011104942, z: 0.025858095}
      rotation: {x: -0.09941147, y: 0.023245553, z: 0.13084193, w: 0.98613256}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb2
      position: {x: 0.016374, y: -0.00529, z: 0.02349136}
      rotation: {x: -0.02606267, y: -0.09668537, z: -0.0036059343, w: 0.9949672}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    - name: RightHandThumb3
      position: {x: 0.02546, y: -0.00764, z: 0.020833}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
      transformModified: 1
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: e8914d097ece7cc48a83d5fccd4098c0,
    type: 3}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
